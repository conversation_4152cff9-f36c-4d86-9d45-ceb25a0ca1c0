"""
Outlook邮件发送模块
提供安全可靠的邮件发送功能
"""

import win32com.client
import pythoncom
import tempfile
import os
import time
from typing import Tuple, Optional, List
from pathlib import Path

from utils.logger import logger, log_function_call, log_task_operation
from config.settings import config_manager


class OutlookSender:
    """Outlook邮件发送器"""
    
    def __init__(self):
        self.outlook = None
        self.timeout = config_manager.get('email.timeout', 30)
        self.auto_delete_sent = config_manager.get('email.auto_delete_sent', True)
    
    @log_function_call
    def _initialize_com(self):
        """初始化COM组件"""
        try:
            pythoncom.CoInitialize()
            self.outlook = win32com.client.Dispatch("Outlook.Application")
            logger.debug("COM组件初始化成功")
        except Exception as e:
            logger.error(f"COM组件初始化失败: {e}")
            raise
    
    @log_function_call
    def _cleanup_com(self):
        """清理COM组件"""
        try:
            if self.outlook:
                self.outlook = None
            pythoncom.CoUninitialize()
            logger.debug("COM组件清理完成")
        except Exception as e:
            logger.warning(f"COM组件清理时出现警告: {e}")
    
    @log_function_call
    def find_draft_by_subject(self, subject_keyword: str) -> Tuple[bool, Optional[object], str]:
        """根据主题关键词查找草稿邮件"""
        try:
            namespace = self.outlook.GetNamespace("MAPI")
            drafts = namespace.GetDefaultFolder(16)  # 草稿箱
            
            logger.info(f"在草稿箱中搜索包含关键词 '{subject_keyword}' 的邮件")
            
            for item in drafts.Items:
                try:
                    subject = getattr(item, 'Subject', '')
                    if subject and subject_keyword.lower() in subject.lower():
                        if self._is_valid_mail(item):
                            logger.info(f"找到匹配的草稿邮件: {subject}")
                            return True, item, f"找到邮件: {subject}"
                        else:
                            logger.warning(f"邮件格式无效: {subject}")
                except Exception as e:
                    logger.warning(f"检查邮件时出错: {e}")
                    continue
            
            message = f"未找到包含关键词 '{subject_keyword}' 的草稿邮件"
            logger.warning(message)
            return False, None, message
            
        except Exception as e:
            error_msg = f"搜索草稿邮件失败: {e}"
            logger.error(error_msg)
            return False, None, error_msg
    
    @log_function_call
    def _is_valid_mail(self, mail_item) -> bool:
        """验证邮件是否有效"""
        try:
            # 检查必要属性
            required_attrs = ['Subject', 'To', 'Body']
            if not all(hasattr(mail_item, attr) for attr in required_attrs):
                return False
            
            # 检查收件人
            if not getattr(mail_item, 'To', '').strip():
                logger.warning("邮件缺少收件人")
                return False
            
            # 检查邮件类型
            try:
                prop_accessor = mail_item.PropertyAccessor
                message_class = prop_accessor.GetProperty("http://schemas.microsoft.com/mapi/proptag/0x001A001E")
                return message_class == "IPM.Note"
            except:
                # 如果无法获取邮件类型，默认认为有效
                return True
                
        except Exception as e:
            logger.warning(f"验证邮件有效性时出错: {e}")
            return False
    
    @log_function_call
    def _send_safely(self, original_mail, include_attachments: bool) -> Tuple[bool, str]:
        """安全发送邮件"""
        new_mail = None
        temp_files = []

        try:
            # 先提取所有需要的信息，避免后续访问已删除的对象
            mail_data = self._extract_mail_data(original_mail, include_attachments)

            # 创建新邮件
            new_mail = self.outlook.CreateItem(0)

            # 设置基本属性
            new_mail.Subject = mail_data['subject']
            new_mail.Body = mail_data['body']
            new_mail.To = mail_data['to']
            new_mail.CC = mail_data['cc']
            new_mail.BCC = mail_data['bcc']

            # 设置HTML内容
            if mail_data['html_body']:
                new_mail.HTMLBody = mail_data['html_body']

            # 添加附件
            for attachment_path in mail_data['attachments']:
                new_mail.Attachments.Add(attachment_path)
                temp_files.append(attachment_path)

            # 发送邮件
            new_mail.Send()
            logger.info(f"邮件发送成功: {mail_data['subject']}")

            # 自动删除原草稿
            if self.auto_delete_sent:
                self._delete_draft_safely(original_mail)

            return True, "邮件发送成功"

        except Exception as e:
            error_msg = f"发送邮件失败: {e}"
            logger.error(error_msg)
            return False, error_msg

        finally:
            # 清理临时文件
            for temp_file in temp_files:
                try:
                    import os
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                except:
                    pass
    
    @log_function_call
    def _extract_mail_data(self, mail_item, include_attachments: bool) -> dict:
        """提取邮件数据，避免后续访问已删除的对象"""
        mail_data = {
            'subject': '',
            'body': '',
            'to': '',
            'cc': '',
            'bcc': '',
            'html_body': '',
            'attachments': []
        }

        try:
            # 提取基本信息
            mail_data['subject'] = getattr(mail_item, 'Subject', '')
            mail_data['body'] = getattr(mail_item, 'Body', '')
            mail_data['to'] = getattr(mail_item, 'To', '')
            mail_data['cc'] = getattr(mail_item, 'CC', '')
            mail_data['bcc'] = getattr(mail_item, 'BCC', '')

            # 提取HTML内容
            try:
                if hasattr(mail_item, 'HTMLBody') and mail_item.HTMLBody:
                    mail_data['html_body'] = mail_item.HTMLBody
            except:
                pass

            # 提取附件
            if include_attachments:
                try:
                    if hasattr(mail_item, 'Attachments') and mail_item.Attachments.Count > 0:
                        temp_dir = tempfile.mkdtemp()

                        for i in range(1, mail_item.Attachments.Count + 1):
                            try:
                                attachment = mail_item.Attachments.Item(i)
                                filename = attachment.FileName
                                temp_file = os.path.join(temp_dir, filename)

                                # 保存附件到临时文件
                                attachment.SaveAsFile(temp_file)
                                mail_data['attachments'].append(temp_file)
                                logger.debug(f"提取附件: {filename}")

                            except Exception as e:
                                logger.warning(f"提取附件 {i} 失败: {e}")
                                continue

                except Exception as e:
                    logger.error(f"处理附件时出错: {e}")

            logger.debug(f"成功提取邮件数据: {mail_data['subject']}")
            return mail_data

        except Exception as e:
            logger.error(f"提取邮件数据失败: {e}")
            return mail_data

    @log_function_call
    def _copy_mail_properties(self, source_mail, target_mail):
        """安全复制邮件属性"""
        properties = [
            ('Subject', ''),
            ('Body', ''),
            ('To', ''),
            ('CC', ''),
            ('BCC', ''),
        ]
        
        for prop_name, default_value in properties:
            try:
                value = getattr(source_mail, prop_name, default_value)
                setattr(target_mail, prop_name, value)
            except Exception as e:
                logger.warning(f"复制属性 {prop_name} 失败: {e}")
                setattr(target_mail, prop_name, default_value)
        
        # 处理HTML内容
        try:
            if hasattr(source_mail, 'HTMLBody') and source_mail.HTMLBody:
                target_mail.HTMLBody = source_mail.HTMLBody
        except Exception as e:
            logger.warning(f"复制HTML内容失败: {e}")
    
    @log_function_call
    def _copy_attachments(self, source_mail, target_mail):
        """安全复制附件"""
        try:
            if not hasattr(source_mail, 'Attachments') or source_mail.Attachments.Count == 0:
                return
            
            # 创建临时目录
            temp_dir = tempfile.mkdtemp()
            logger.debug(f"创建临时目录: {temp_dir}")
            
            try:
                for i in range(1, source_mail.Attachments.Count + 1):
                    try:
                        attachment = source_mail.Attachments.Item(i)
                        filename = attachment.FileName
                        temp_file = os.path.join(temp_dir, filename)
                        
                        # 保存附件到临时文件
                        attachment.SaveAsFile(temp_file)
                        
                        # 添加到新邮件
                        target_mail.Attachments.Add(temp_file)
                        logger.debug(f"成功复制附件: {filename}")
                        
                    except Exception as e:
                        logger.warning(f"复制附件 {i} 失败: {e}")
                        continue
                        
            finally:
                # 清理临时文件
                self._cleanup_temp_dir(temp_dir)
                
        except Exception as e:
            logger.error(f"处理附件时出错: {e}")
    
    @log_function_call
    def _cleanup_temp_dir(self, temp_dir: str):
        """清理临时目录"""
        try:
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            logger.debug(f"清理临时目录: {temp_dir}")
        except Exception as e:
            logger.warning(f"清理临时目录失败: {e}")
    
    @log_function_call
    def _delete_draft_safely(self, draft_mail):
        """安全删除草稿邮件"""
        try:
            # 先获取主题，避免删除后无法访问
            subject = getattr(draft_mail, 'Subject', '未知邮件')

            # 检查邮件是否仍然存在
            try:
                # 尝试访问邮件的一个属性来检查是否仍然有效
                _ = draft_mail.Subject
                draft_mail.Delete()
                logger.info(f"已删除草稿邮件: {subject}")
            except Exception as inner_e:
                # 如果邮件已经不存在，这是正常的
                if "已经被移动或删除" in str(inner_e) or "moved or deleted" in str(inner_e).lower():
                    logger.info(f"草稿邮件已被自动删除: {subject}")
                else:
                    logger.warning(f"删除草稿邮件时出错: {inner_e}")

        except Exception as e:
            logger.warning(f"删除草稿邮件失败: {e}")
    
    @log_task_operation("发送邮件")
    def send_draft_by_subject(self, subject_keyword: str, include_attachments: bool = True) -> Tuple[bool, str]:
        """根据主题关键词发送草稿邮件"""
        try:
            # 初始化COM
            self._initialize_com()
            
            # 查找草稿邮件
            found, draft_mail, message = self.find_draft_by_subject(subject_keyword)
            if not found:
                return False, message
            
            # 发送邮件
            success, send_message = self._send_safely(draft_mail, include_attachments)
            return success, send_message
            
        except Exception as e:
            error_msg = f"发送邮件过程中出现异常: {e}"
            logger.exception(error_msg)
            return False, error_msg
            
        finally:
            # 清理COM
            self._cleanup_com()
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试Outlook连接"""
        try:
            self._initialize_com()
            namespace = self.outlook.GetNamespace("MAPI")
            drafts = namespace.GetDefaultFolder(16)
            count = drafts.Items.Count
            message = f"连接成功，草稿箱中有 {count} 封邮件"
            logger.info(message)
            return True, message
        except Exception as e:
            error_msg = f"连接Outlook失败: {e}"
            logger.error(error_msg)
            return False, error_msg
        finally:
            self._cleanup_com()
