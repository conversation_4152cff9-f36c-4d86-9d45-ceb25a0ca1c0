# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller配置文件 - Outlook邮件定时发送工具
优化配置，减少打包大小，提高性能
"""

import os
import sys
from pathlib import Path

# 项目根目录
project_root = Path(__file__).parent

# 分析配置
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=[
        # 包含项目图标
        ('1.ico', '.'),
        # 包含配置目录（如果存在默认配置）
        ('config', 'config'),
    ],
    hiddenimports=[
        # Windows COM相关
        'win32com.client',
        'win32com.client.gencache',
        'pythoncom',
        'pywintypes',
        'win32api',
        'win32gui',
        'win32con',
        
        # 系统托盘相关
        'pystray',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        
        # 系统信息
        'psutil',
        
        # tkinter相关
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        
        # 项目模块
        'config',
        'config.settings',
        'gui',
        'gui.main_window',
        'gui.modern_task_manager',
        'gui.modern_theme',
        'gui.notification_system',
        'gui.system_tray',
        'gui.task_editor',
        'gui.components',
        'gui.help_info',
        'email_sender',
        'email_sender.outlook_sender',
        'scheduler',
        'scheduler.task_scheduler',
        'scheduler.task_model',
        'utils',
        'utils.logger',
        'utils.helpers',
        'utils.single_instance',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除未使用的可选依赖
        'dataclasses_json',
        'dateutil',
        'jsonschema',
        'click',
        'tqdm',
        
        # 排除不必要的标准库模块
        'unittest',
        'test',
        'tests',
        'pytest',
        'nose',
        'doctest',
        'pdb',
        'profile',
        'cProfile',
        'pstats',
        'trace',
        'tracemalloc',
        
        # 排除开发工具
        'IPython',
        'jupyter',
        'notebook',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'sklearn',
        
        # 排除网络相关（如果不需要）
        'urllib3',
        'requests',
        'http.server',
        'socketserver',
        
        # 排除其他GUI框架
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
        'wx',
        'kivy',
        
        # 排除数据库相关（如果不需要）
        'sqlite3',
        'mysql',
        'postgresql',
        'pymongo',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 优化：移除不必要的二进制文件
def filter_binaries(binaries):
    """过滤不必要的二进制文件"""
    excluded_patterns = [
        'api-ms-win-',  # Windows API集
        'ucrtbase',     # 通用C运行时
        '_testcapi',    # 测试模块
        '_test',        # 测试模块
    ]
    
    filtered = []
    for name, path, type_info in binaries:
        should_exclude = any(pattern in name.lower() for pattern in excluded_patterns)
        if not should_exclude:
            filtered.append((name, path, type_info))
    
    return filtered

# 应用二进制文件过滤
a.binaries = filter_binaries(a.binaries)

# PYZ配置 - 启用压缩
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# EXE配置
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='OutlookScheduler',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 启用UPX压缩
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='1.ico',  # 设置程序图标
)

# COLLECT配置 - 单目录模式
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,  # 启用UPX压缩
    upx_exclude=[],
    name='OutlookScheduler'
)
