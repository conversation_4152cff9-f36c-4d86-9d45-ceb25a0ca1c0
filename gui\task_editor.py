"""
任务编辑器界面
提供任务创建和编辑功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from typing import Optional, Callable
from pathlib import Path

from scheduler.task_model import EmailTask
from gui.components import DateTimePicker, show_error, show_info
from gui.modern_theme import ModernTheme, configure_modern_style
from utils.logger import logger


class TaskEditorDialog:
    """任务编辑器对话框"""
    
    def __init__(self, parent, task: Optional[EmailTask] = None, 
                 on_save: Optional[Callable[[EmailTask], None]] = None):
        self.parent = parent
        self.task = task
        self.on_save = on_save
        self.result = None
        self.colors = ModernTheme.COLORS
        self.fonts = ModernTheme.FONTS

        # 配置现代化样式
        self.style = configure_modern_style()

        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("编辑任务" if task else "新建任务")
        self.dialog.configure(bg=self.colors['bg_primary'])
        self.dialog.geometry("500x600")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 设置图标
        self.set_dialog_icon()

        # 居中显示
        self.center_dialog()
        
        # 创建界面
        self.create_widgets()
        
        # 如果是编辑模式，填充数据
        if self.task:
            self.load_task_data()
        
        # 绑定事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_cancel)
        self.dialog.bind('<Escape>', lambda e: self.on_cancel())

    def set_dialog_icon(self):
        """设置对话框图标"""
        try:
            # 获取图标文件路径
            icon_path = Path(__file__).parent.parent / "1.ico"

            if icon_path.exists():
                self.dialog.iconbitmap(str(icon_path))
                logger.debug(f"成功设置对话框图标: {icon_path}")
            else:
                logger.warning(f"图标文件不存在: {icon_path}")

        except Exception as e:
            logger.error(f"设置对话框图标失败: {e}")

    def center_dialog(self):
        """居中显示对话框 - 紧凑版本"""
        self.dialog.update_idletasks()
        width, height = 480, 580  # 增加高度以显示按钮
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
        self.dialog.minsize(width, height)
    
    def create_widgets(self):
        """创建现代化界面组件"""
        # 主框架 - 紧凑版本
        main_frame = ttk.Frame(self.dialog, padding=8, style='Modern.TFrame')
        main_frame.pack(fill='both', expand=True)

        # 任务名称 - 紧凑版本
        name_frame = ttk.LabelFrame(main_frame, text="任务信息", padding=6,
                                   style='Modern.TLabelframe')
        name_frame.pack(fill='x', pady=(0, 6))
        
        ttk.Label(name_frame, text="任务名称:", style='Modern.TLabel').grid(row=0, column=0, sticky='w', pady=2)
        self.name_var = tk.StringVar()
        self.name_entry = ttk.Entry(name_frame, textvariable=self.name_var, width=40,
                                   style='Modern.TEntry')
        self.name_entry.grid(row=0, column=1, sticky='ew', padx=(6, 0), pady=2)

        ttk.Label(name_frame, text="邮件主题关键词:", style='Modern.TLabel').grid(row=1, column=0, sticky='w', pady=2)
        self.subject_var = tk.StringVar()
        self.subject_entry = ttk.Entry(name_frame, textvariable=self.subject_var, width=40,
                                      style='Modern.TEntry')
        self.subject_entry.grid(row=1, column=1, sticky='ew', padx=(6, 0), pady=2)
        
        # 配置列权重
        name_frame.columnconfigure(1, weight=1)
        
        # 发送时间
        self.datetime_picker = DateTimePicker(main_frame, "发送时间")
        self.datetime_picker.pack(fill='x', pady=(0, 10))
        
        # 快速时间设置
        quick_time_frame = ttk.LabelFrame(main_frame, text="快速设置", padding=4,
                                         style='Modern.TLabelframe')
        quick_time_frame.pack(fill='x', pady=(0, 4))

        quick_buttons = [
            ("1分钟后", 1),
            ("5分钟后", 5),
            ("10分钟后", 10),
            ("30分钟后", 30),
            ("1小时后", 60),
            ("明天此时", 24 * 60)
        ]

        for i, (text, minutes) in enumerate(quick_buttons):
            btn = ttk.Button(quick_time_frame, text=text,
                           style='Editor.Quick.TButton',
                           command=lambda m=minutes: self.set_quick_time(m))
            btn.grid(row=i // 3, column=i % 3, padx=3, pady=3, sticky='ew')
        
        # 配置列权重
        for i in range(3):
            quick_time_frame.columnconfigure(i, weight=1)
        
        # 选项设置
        options_frame = ttk.LabelFrame(main_frame, text="选项", padding=4,
                                      style='Modern.TLabelframe')
        options_frame.pack(fill='x', pady=(0, 4))

        self.include_attachments_var = tk.BooleanVar(value=True)
        attachments_cb = ttk.Checkbutton(options_frame, text="包含附件",
                                        style='Modern.TCheckbutton',
                                        variable=self.include_attachments_var)
        attachments_cb.pack(anchor='w')

        # 高级设置
        advanced_frame = ttk.LabelFrame(main_frame, text="高级设置", padding=4,
                                       style='Modern.TLabelframe')
        advanced_frame.pack(fill='x', pady=(0, 4))

        ttk.Label(advanced_frame, text="最大重试次数:", style='Modern.TLabel').grid(row=0, column=0, sticky='w', pady=2)
        self.max_retries_var = tk.StringVar(value="3")
        max_retries_spin = ttk.Spinbox(advanced_frame, from_=0, to=10, width=10,
                                      style='Modern.TSpinbox',
                                      textvariable=self.max_retries_var)
        max_retries_spin.grid(row=0, column=1, sticky='w', padx=(6, 0), pady=2)
        
        # 按钮框架 - 紧凑版本
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(8, 0))

        # 测试连接按钮 - 使用新的蓝色样式
        test_btn = ttk.Button(button_frame, text="🔗 测试连接",
                             style='Editor.Test.TButton',
                             command=self.test_outlook_connection)
        test_btn.pack(side='left', padx=(0, 8))

        # 右侧按钮
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side='right')

        save_btn = ttk.Button(right_buttons, text="💾 保存",
                             style='Editor.Save.TButton',
                             command=self.on_save_click)
        save_btn.pack(side='right', padx=(8, 0))

        cancel_btn = ttk.Button(right_buttons, text="❌ 取消",
                               style='Editor.Cancel.TButton',
                               command=self.on_cancel)
        cancel_btn.pack(side='right')
        
        # 设置焦点
        self.name_entry.focus_set()
    
    def set_quick_time(self, minutes: int):
        """设置快速时间"""
        target_time = datetime.now() + timedelta(minutes=minutes)
        self.datetime_picker.set_datetime(target_time)
    
    def load_task_data(self):
        """加载任务数据"""
        if not self.task:
            return
        
        self.name_var.set(self.task.name)
        self.subject_var.set(self.task.subject_keyword)
        self.datetime_picker.set_datetime(self.task.scheduled_time)
        self.include_attachments_var.set(self.task.include_attachments)
        self.max_retries_var.set(str(self.task.max_retries))
    
    def validate_input(self) -> bool:
        """验证输入"""
        # 检查任务名称
        if not self.name_var.get().strip():
            show_error(self.dialog, "输入错误", "请输入任务名称")
            self.name_entry.focus_set()
            return False
        
        # 检查主题关键词
        if not self.subject_var.get().strip():
            show_error(self.dialog, "输入错误", "请输入邮件主题关键词")
            self.subject_entry.focus_set()
            return False
        
        # 检查发送时间
        scheduled_time = self.datetime_picker.get_datetime()
        if not scheduled_time:
            show_error(self.dialog, "输入错误", "请选择有效的发送时间")
            return False
        
        # 检查时间不能早于当前时间
        if scheduled_time <= datetime.now():
            show_error(self.dialog, "输入错误", "发送时间不能早于当前时间")
            return False
        
        # 检查重试次数
        try:
            max_retries = int(self.max_retries_var.get())
            if max_retries < 0:
                raise ValueError()
        except ValueError:
            show_error(self.dialog, "输入错误", "重试次数必须是非负整数")
            return False
        
        return True
    
    def test_outlook_connection(self):
        """测试Outlook连接"""
        try:
            # 立即显示开始测试的提示
            from gui.components import show_info, show_error

            # 禁用按钮防止重复点击
            test_btn = None
            for widget in self.dialog.winfo_children():
                if hasattr(widget, 'winfo_children'):
                    for child in widget.winfo_children():
                        if hasattr(child, 'winfo_children'):
                            for grandchild in child.winfo_children():
                                if isinstance(grandchild, ttk.Button) and "测试连接" in str(grandchild.cget('text')):
                                    test_btn = grandchild
                                    break

            if test_btn:
                test_btn.config(state='disabled', text='测试中...')

            def test_connection():
                try:
                    from email_sender.outlook_sender import OutlookSender
                    sender = OutlookSender()
                    success, message = sender.test_connection()

                    def show_result():
                        # 恢复按钮状态
                        if test_btn:
                            test_btn.config(state='normal', text='🔗 测试连接')

                        if success:
                            show_info(self.dialog, "连接测试成功", f"✅ Outlook连接正常！\n\n{message}")
                        else:
                            show_error(self.dialog, "连接测试失败", f"❌ 无法连接到Outlook\n\n{message}")

                    self.dialog.after(0, show_result)

                except Exception as e:
                    def show_error_result():
                        # 恢复按钮状态
                        if test_btn:
                            test_btn.config(state='normal', text='🔗 测试连接')
                        show_error(self.dialog, "连接测试错误", f"❌ 测试过程中发生错误：\n\n{str(e)}")

                    self.dialog.after(0, show_error_result)

            # 在新线程中执行测试
            import threading
            threading.Thread(target=test_connection, daemon=True).start()

        except Exception as e:
            show_error(self.dialog, "启动测试失败", f"❌ 无法启动连接测试：\n\n{str(e)}")
    
    def on_save_click(self):
        """保存按钮点击事件"""
        if not self.validate_input():
            return
        
        try:
            # 创建或更新任务
            if self.task:
                # 编辑模式
                self.task.name = self.name_var.get().strip()
                self.task.subject_keyword = self.subject_var.get().strip()
                self.task.scheduled_time = self.datetime_picker.get_datetime()
                self.task.include_attachments = self.include_attachments_var.get()
                self.task.max_retries = int(self.max_retries_var.get())
                self.result = self.task
            else:
                # 新建模式
                self.result = EmailTask.create_new(
                    name=self.name_var.get().strip(),
                    subject_keyword=self.subject_var.get().strip(),
                    scheduled_time=self.datetime_picker.get_datetime(),
                    include_attachments=self.include_attachments_var.get()
                )
                self.result.max_retries = int(self.max_retries_var.get())
            
            # 调用保存回调
            if self.on_save:
                self.on_save(self.result)
            
            logger.info(f"任务保存成功: {self.result.name}")
            self.dialog.destroy()
            
        except Exception as e:
            logger.error(f"保存任务失败: {e}")
            show_error(self.dialog, "保存失败", f"保存任务时发生错误：\n{str(e)}")
    
    def on_cancel(self):
        """取消按钮点击事件"""
        self.result = None
        self.dialog.destroy()
    
    def show(self) -> Optional[EmailTask]:
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result


def show_task_editor(parent, task: Optional[EmailTask] = None) -> Optional[EmailTask]:
    """显示任务编辑器"""
    editor = TaskEditorDialog(parent, task)
    return editor.show()
