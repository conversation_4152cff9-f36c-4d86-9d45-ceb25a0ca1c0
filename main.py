#!/usr/bin/env python3
"""
Outlook邮件定时发送工具 v2.0
主程序入口

功能特性：
- 多任务管理
- 定时发送邮件
- 任务调度
- 历史记录
- 错误重试
- 配置管理
- 日志记录
"""

import sys
import os
import traceback
import atexit
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'win32com.client',
        'tkinter',
        'threading',
        'datetime',
        'json',
        'pathlib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'win32com.client':
                import win32com.client
            elif package == 'tkinter':
                import tkinter
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("错误：缺少必要的依赖包：")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请安装缺少的依赖包：")
        if 'win32com.client' in missing_packages:
            print("  pip install pywin32")
        print("\n然后重新运行程序。")
        return False
    
    return True


def check_system_requirements():
    """检查系统要求"""
    # 检查操作系统
    if sys.platform != 'win32':
        print("错误：此程序仅支持Windows操作系统")
        return False
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误：需要Python 3.7或更高版本")
        return False
    
    # 检查Outlook是否安装
    try:
        from utils.helpers import check_outlook_installed
        if not check_outlook_installed():
            print("警告：未检测到Outlook，请确保已安装Microsoft Outlook")
            response = input("是否继续运行程序？(y/N): ")
            if response.lower() != 'y':
                return False
    except Exception:
        print("警告：无法检测Outlook安装状态")
    
    return True


def setup_environment():
    """设置运行环境"""
    try:
        # 创建必要的目录
        from utils.helpers import ensure_directory
        
        directories = [
            'config',
            'logs',
            'backups',
            'temp'
        ]
        
        for directory in directories:
            ensure_directory(directory)
        
        # 初始化日志系统
        from utils.logger import logger_manager
        logger_manager.setup_logger()
        
        return True
        
    except Exception as e:
        print(f"设置环境失败: {e}")
        return False


def handle_exception(exc_type, exc_value, exc_traceback):
    """全局异常处理"""
    if issubclass(exc_type, KeyboardInterrupt):
        # 用户中断，正常退出
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    # 记录异常到日志
    try:
        from utils.logger import logger
        logger.critical("未处理的异常", exc_info=(exc_type, exc_value, exc_traceback))
    except:
        pass
    
    # 显示错误对话框
    try:
        import tkinter as tk
        from tkinter import messagebox
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        error_msg = f"程序发生严重错误：\n\n{exc_type.__name__}: {exc_value}\n\n"
        error_msg += "详细错误信息已记录到日志文件中。\n"
        error_msg += "请联系开发者或查看日志文件获取更多信息。"
        
        messagebox.showerror("程序错误", error_msg)
        root.destroy()
        
    except:
        # 如果无法显示GUI错误，则打印到控制台
        print(f"严重错误: {exc_type.__name__}: {exc_value}")
        traceback.print_exception(exc_type, exc_value, exc_traceback)


def main():
    """主函数"""
    try:
        print("Outlook邮件定时发送工具 v2.0")
        print("=" * 40)

        # 检查单实例运行
        print("检查程序实例...")
        from utils.single_instance import single_instance_manager

        if single_instance_manager.is_already_running():
            single_instance_manager.handle_existing_instance()
            return 0

        # 注册清理函数
        atexit.register(single_instance_manager.cleanup)

        # 检查系统要求
        print("检查系统要求...")
        if not check_system_requirements():
            input("按回车键退出...")
            return 1
        
        # 检查依赖包
        print("检查依赖包...")
        if not check_dependencies():
            input("按回车键退出...")
            return 1
        
        # 设置环境
        print("初始化环境...")
        if not setup_environment():
            print("环境初始化失败")
            input("按回车键退出...")
            return 1
        
        # 设置全局异常处理
        sys.excepthook = handle_exception
        
        # 导入并启动主窗口
        print("启动应用程序...")
        from gui.main_window import ModernMainWindow
        from utils.logger import logger

        logger.info("现代化应用程序启动")

        # 创建并运行现代化主窗口
        app = ModernMainWindow()
        app.run()
        
        logger.info("应用程序正常退出")
        return 0
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return 0
        
    except Exception as e:
        print(f"启动失败: {e}")
        traceback.print_exc()
        input("按回车键退出...")
        return 1


def show_help():
    """显示帮助信息"""
    help_text = """
Outlook邮件定时发送工具 v2.0

用法：
    python main.py              # 启动GUI界面
    python main.py --help       # 显示此帮助信息
    python main.py --version    # 显示版本信息

功能特性：
- 多任务管理：支持同时管理多个邮件发送任务
- 定时发送：精确的时间控制，支持秒级定时
- 任务调度：智能的任务调度系统，支持暂停、恢复、取消
- 历史记录：完整的发送历史记录和统计
- 错误重试：自动重试机制，提高发送成功率
- 配置管理：灵活的配置系统，支持个性化设置
- 日志记录：详细的日志记录，便于问题排查

系统要求：
- Windows操作系统
- Python 3.7+
- Microsoft Outlook
- pywin32包

使用说明：
1. 确保Outlook已安装并配置好邮箱账户
2. 将要发送的邮件保存在Outlook草稿箱中
3. 启动程序，创建发送任务
4. 设置邮件主题关键词（用于匹配草稿箱中的邮件）
5. 设置发送时间
6. 启动调度器，等待自动发送

注意事项：
- 邮件主题关键词用于在草稿箱中查找对应的邮件
- 发送成功后，原草稿邮件会被自动删除（可配置）
- 支持包含附件的邮件发送
- 建议定期备份重要的任务配置

更多信息请查看程序内的帮助文档。
    """
    print(help_text)


def show_version():
    """显示版本信息"""
    version_info = """
Outlook邮件定时发送工具
版本：2.0
开发者：AI Assistant
Python版本：{python_version}
平台：{platform}
    """.format(
        python_version=sys.version.split()[0],
        platform=sys.platform
    )
    print(version_info)


if __name__ == "__main__":
    # 处理命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ['--help', '-h', 'help']:
            show_help()
            sys.exit(0)
        elif arg in ['--version', '-v', 'version']:
            show_version()
            sys.exit(0)
        else:
            print(f"未知参数: {sys.argv[1]}")
            print("使用 --help 查看帮助信息")
            sys.exit(1)
    
    # 运行主程序
    exit_code = main()
    sys.exit(exit_code)
