"""
现代化主题和样式管理
提供统一的颜色、字体和样式配置
"""

import tkinter as tk
from tkinter import ttk


class ModernTheme:
    """现代化主题配置"""
    
    # 颜色配置
    COLORS = {
        # 主色调
        'primary': '#2563eb',           # 蓝色
        'primary_dark': '#1d4ed8',      # 深蓝色
        'primary_light': '#3b82f6',     # 浅蓝色
        
        # 背景色
        'bg_primary': "#f8f7f8",        # 主背景
        'bg_secondary': "#faf9fa",      # 次要背景
        'bg_tertiary': "#29b1a1",       # 第三背景
        'bg_dark': '#1e293b',           # 深色背景
        
        # 文字颜色
        'text_primary': '#1e293b',      # 主文字
        'text_secondary': "#080808",    # 次要文字
        'text_muted': "#070707",        # 弱化文字
        'text_white': "#0c0c0c",        # 白色文字
        
        # 状态颜色
        'success': "#045F14",           # 成功绿色
        'success_light': "#095202",     # 浅绿色背景
        'warning': '#f59e0b',           # 警告橙色
        'warning_light': '#fef3c7',     # 浅橙色背景
        'error': '#ef4444',             # 错误红色
        'error_light': '#fee2e2',       # 浅红色背景
        'info': "#010008",              # 信息蓝色
        'info_light': "#5F0404",        # 浅蓝色背景
        # 边框颜色
        'border': "#0c82e2",            # 默认边框
        'border_focus': '#3b82f6',      # 焦点边框
        'border_hover': "#08556d",      # 悬停边框
        
        # 任务状态颜色
        'task_pending': '#fbbf24',      # 等待中
        'task_running': '#3b82f6',      # 运行中
        'task_completed': "#08fa31",    # 已完成
        'task_failed': "#f30c0c",       # 失败
        'task_cancelled': "#f10606",    # 已取消
        'task_paused': "#f81505",       # 已暂停
    }
    
    # 字体配置 - 增大字体尺寸
    FONTS = {
        'default': ('Microsoft YaHei UI', 11),      # 从9增加到10
        'heading': ('Microsoft YaHei UI', 16, 'bold'),  # 从12增加到14
        'subheading': ('Microsoft YaHei UI', 14, 'bold'),  # 从10增加到12
        'small': ('Microsoft YaHei UI', 12),        # 从9增加到10
        'monospace': ('Consolas', 11),              # 从9增加到10
    }
    
    # 尺寸配置
    SIZES = {
        'padding_small': 4,
        'padding_medium': 8,
        'padding_large': 16,
        'padding_xlarge': 24,
        'border_radius': 6,
        'button_height': 32,
        'input_height': 32,
    }


def configure_modern_style():
    """配置现代化ttk样式"""
    style = ttk.Style()

    # 设置主题
    try:
        style.theme_use('clam')
    except:
        style.theme_use('default')

    colors = ModernTheme.COLORS
    fonts = ModernTheme.FONTS

    # 配置字体渲染优化
    import tkinter as tk
    root = tk._default_root
    if root:
        # 启用字体平滑
        try:
            root.tk.call('tk', 'scaling', 1.0)
        except:
            pass
    
    # 配置Frame样式
    style.configure('Modern.TFrame',
                   background=colors['bg_primary'],
                   relief='flat')
    
    style.configure('Card.TFrame',
                   background=colors['bg_primary'],
                   relief='solid',
                   borderwidth=1)
    
    style.configure('Sidebar.TFrame',
                   background=colors['bg_secondary'],
                   relief='flat')
    
    # 配置Label样式
    style.configure('Modern.TLabel',
                   background=colors['bg_primary'],
                   foreground=colors['text_primary'],
                   font=fonts['default'])
    
    style.configure('Heading.TLabel',
                   background=colors['bg_primary'],
                   foreground=colors['text_primary'],
                   font=fonts['heading'])
    
    style.configure('Subheading.TLabel',
                   background=colors['bg_primary'],
                   foreground=colors['text_secondary'],
                   font=fonts['subheading'])
    
    style.configure('Muted.TLabel',
                   background=colors['bg_primary'],
                   foreground=colors['text_muted'],
                   font=fonts['small'])
    
    # 配置Button样式
    style.configure('Modern.TButton',
                   font=fonts['default'],
                   padding=(16, 8),
                   relief='flat',
                   focuscolor='none')

    style.map('Modern.TButton',
              background=[('hover', colors['primary_light']),
                         ('active', colors['primary_dark']),
                         ('pressed', colors['primary_dark']),
                         ('!active', colors['primary'])],
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', colors['primary_light']),
                          ('active', colors['primary_dark']),
                          ('!active', colors['primary'])])

    # 主要按钮样式
    style.configure('Primary.TButton',
                   font=fonts['default'],
                   padding=(18, 10),      # 增加内边距以适应更大字体
                   relief='flat',
                   focuscolor='none')

    style.map('Primary.TButton',
              background=[('hover', colors['primary_light']),
                         ('active', colors['primary_dark']),
                         ('pressed', colors['primary_dark']),
                         ('!active', colors['primary'])],
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', colors['primary_light']),
                          ('active', colors['primary_dark']),
                          ('!active', colors['primary'])])

    # 次要按钮样式
    style.configure('Secondary.TButton',
                   font=fonts['default'],
                   padding=(14, 8),       # 增加内边距
                   relief='flat',
                   focuscolor='none')

    style.map('Secondary.TButton',
              background=[('hover', colors['border_hover']),
                         ('active', colors['bg_tertiary']),
                         ('pressed', colors['border_hover']),
                         ('!active', colors['bg_secondary'])],
              foreground=[('hover', colors['text_primary']),
                         ('active', colors['text_primary']),
                         ('pressed', colors['text_primary']),
                         ('!active', colors['text_secondary'])],
              bordercolor=[('hover', colors['border_hover']),
                          ('active', colors['border']),
                          ('!active', colors['border'])])

    # 危险按钮样式
    style.configure('Danger.TButton',
                   font=fonts['default'],
                   padding=(12, 6),
                   relief='flat',
                   focuscolor='none')

    style.map('Danger.TButton',
              background=[('hover', '#dc2626'),
                         ('active', '#b91c1c'),
                         ('pressed', '#b91c1c'),
                         ('!active', colors['error'])],
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', '#dc2626'),
                          ('active', '#b91c1c'),
                          ('!active', colors['error'])])

    # 成功按钮样式
    style.configure('Success.TButton',
                   font=fonts['default'],
                   padding=(12, 6),
                   relief='flat',
                   focuscolor='none')

    style.map('Success.TButton',
              background=[('hover', '#059669'),
                         ('active', '#047857'),
                         ('pressed', '#047857'),
                         ('!active', colors['success'])],
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', '#059669'),
                          ('active', '#047857'),
                          ('!active', colors['success'])])

    # 警告按钮样式
    style.configure('Warning.TButton',
                   font=fonts['default'],
                   padding=(12, 6),
                   relief='flat',
                   focuscolor='none')

    style.map('Warning.TButton',
              background=[('hover', '#d97706'),
                         ('active', '#b45309'),
                         ('pressed', '#b45309'),
                         ('!active', colors['warning'])],
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', '#d97706'),
                          ('active', '#b45309'),
                          ('!active', colors['warning'])])

    # 紧凑按钮样式
    # 紧凑主要按钮
    style.configure('Compact.Primary.TButton',
                   font=fonts['default'],  # 使用默认字体而不是小字体
                   padding=(10, 6),       # 增加内边距以适应更大字体
                   relief='flat',
                   focuscolor='none')

    style.map('Compact.Primary.TButton',
              background=[('hover', colors['primary_light']),
                         ('active', colors['primary_dark']),
                         ('pressed', colors['primary_dark']),
                         ('!active', colors['primary'])],
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', colors['primary_light']),
                          ('active', colors['primary_dark']),
                          ('!active', colors['primary'])])

    # 紧凑次要按钮
    style.configure('Compact.Secondary.TButton',
                   font=fonts['default'],  # 使用默认字体
                   padding=(10, 6),       # 增加内边距
                   relief='flat',
                   focuscolor='none')

    style.map('Compact.Secondary.TButton',
              background=[('hover', colors['border_hover']),
                         ('active', colors['bg_tertiary']),
                         ('pressed', colors['border_hover']),
                         ('!active', colors['bg_secondary'])],
              foreground=[('hover', colors['text_primary']),
                         ('active', colors['text_primary']),
                         ('pressed', colors['text_primary']),
                         ('!active', colors['text_secondary'])],
              bordercolor=[('hover', colors['border_hover']),
                          ('active', colors['border']),
                          ('!active', colors['border'])])

    # 紧凑成功按钮
    style.configure('Compact.Success.TButton',
                   font=fonts['default'],  # 使用默认字体
                   padding=(10, 6),       # 增加内边距
                   relief='flat',
                   focuscolor='none')

    style.map('Compact.Success.TButton',
              background=[('hover', '#059669'),
                         ('active', '#047857'),
                         ('pressed', '#047857'),
                         ('!active', colors['success'])],
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', '#059669'),
                          ('active', '#047857'),
                          ('!active', colors['success'])])

    # 紧凑警告按钮
    style.configure('Compact.Warning.TButton',
                   font=fonts['default'],  # 使用默认字体
                   padding=(10, 6),       # 增加内边距
                   relief='flat',
                   focuscolor='none')

    style.map('Compact.Warning.TButton',
              background=[('hover', '#d97706'),
                         ('active', '#b45309'),
                         ('pressed', '#b45309'),
                         ('!active', colors['warning'])],
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', '#d97706'),
                          ('active', '#b45309'),
                          ('!active', colors['warning'])])

    # 紧凑危险按钮
    style.configure('Compact.Danger.TButton',
                   font=fonts['default'],  # 使用默认字体
                   padding=(10, 6),       # 增加内边距
                   relief='flat',
                   focuscolor='none')

    style.map('Compact.Danger.TButton',
              background=[('hover', '#dc2626'),
                         ('active', '#b91c1c'),
                         ('pressed', '#b91c1c'),
                         ('!active', colors['error'])],
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', '#dc2626'),
                          ('active', '#b91c1c'),
                          ('!active', colors['error'])])
    
    # 配置Entry样式
    style.configure('Modern.TEntry',
                   font=fonts['default'],
                   padding=(10, 8),      # 增加内边距以适应更大字体
                   relief='solid',
                   borderwidth=1,
                   focuscolor='none')

    style.map('Modern.TEntry',
              bordercolor=[('focus', colors['border_focus']),
                          ('hover', colors['primary_light']),
                          ('!focus', colors['border'])],
              lightcolor=[('focus', colors['border_focus']),
                         ('hover', colors['primary_light']),
                         ('!focus', colors['border'])],
              darkcolor=[('focus', colors['border_focus']),
                        ('hover', colors['primary_light']),
                        ('!focus', colors['border'])])
    
    # 配置Combobox样式
    style.configure('Modern.TCombobox',
                   font=fonts['default'],
                   padding=(10, 8),      # 增加内边距
                   relief='solid',
                   borderwidth=1,
                   focuscolor='none')

    style.map('Modern.TCombobox',
              bordercolor=[('focus', colors['border_focus']),
                          ('hover', colors['primary_light']),
                          ('!focus', colors['border'])],
              lightcolor=[('focus', colors['border_focus']),
                         ('hover', colors['primary_light']),
                         ('!focus', colors['border'])],
              darkcolor=[('focus', colors['border_focus']),
                        ('hover', colors['primary_light']),
                        ('!focus', colors['border'])])
    
    # 配置Treeview样式
    style.configure('Modern.Treeview',
                   font=fonts['default'],
                   background=colors['bg_primary'],
                   foreground=colors['text_primary'],
                   fieldbackground=colors['bg_primary'],
                   borderwidth=1,
                   relief='solid')
    
    style.configure('Modern.Treeview.Heading',
                   font=fonts['subheading'],
                   background=colors['bg_secondary'],
                   foreground=colors['text_primary'],
                   relief='flat',
                   borderwidth=1)
    
    style.map('Modern.Treeview',
              background=[('selected', colors['primary_light']),
                         ('hover', colors['bg_secondary'])],
              foreground=[('selected', colors['text_white']),
                         ('hover', colors['text_primary'])])

    style.map('Modern.Treeview.Heading',
              background=[('active', colors['bg_tertiary']),
                         ('hover', colors['border_hover'])])

    # 紧凑Treeview样式
    style.configure('Compact.Treeview',
                   font=fonts['default'],  # 使用默认字体
                   background=colors['bg_primary'],
                   foreground=colors['text_primary'],
                   fieldbackground=colors['bg_primary'],
                   borderwidth=1,
                   relief='solid',
                   rowheight=24)  # 增加行高以适应更大字体

    style.configure('Compact.Treeview.Heading',
                   font=fonts['default'],  # 使用默认字体
                   background=colors['bg_secondary'],
                   foreground=colors['text_primary'],
                   relief='flat',
                   borderwidth=1)

    style.map('Compact.Treeview',
              background=[('selected', colors['primary_light']),
                         ('hover', colors['bg_secondary'])],
              foreground=[('selected', colors['text_white']),
                         ('hover', colors['text_primary'])])

    style.map('Compact.Treeview.Heading',
              background=[('active', colors['bg_tertiary']),
                         ('hover', colors['border_hover'])])
    
    # 配置LabelFrame样式
    style.configure('Modern.TLabelframe',
                   background=colors['bg_primary'],
                   relief='solid',
                   borderwidth=1)
    
    style.configure('Modern.TLabelframe.Label',
                   background=colors['bg_primary'],
                   foreground=colors['text_primary'],
                   font=fonts['subheading'])
    
    # 配置Progressbar样式
    style.configure('Modern.Horizontal.TProgressbar',
                   background=colors['primary'],
                   troughcolor=colors['bg_tertiary'],
                   borderwidth=0,
                   lightcolor=colors['primary'],
                   darkcolor=colors['primary'])
    
    # 配置Checkbutton样式
    style.configure('Modern.TCheckbutton',
                   font=fonts['default'],
                   background=colors['bg_primary'],
                   foreground=colors['text_primary'])
    
    # 配置Separator样式
    style.configure('Modern.TSeparator',
                   background=colors['border'])

    # 任务编辑器专用按钮样式
    # 编辑器保存按钮 - 渐变绿色
    style.configure('Editor.Save.TButton',
                   font=fonts['default'],
                   padding=(12, 8),
                   relief='flat',
                   focuscolor='none')

    style.map('Editor.Save.TButton',
              background=[('hover', '#16a34a'),
                         ('active', '#15803d'),
                         ('pressed', '#15803d'),
                         ('!active', '#22c55e')],  # 绿色保存按钮
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', '#16a34a'),
                          ('active', '#15803d'),
                          ('!active', '#22c55e')])

    # 编辑器取消按钮 - 渐变灰色
    style.configure('Editor.Cancel.TButton',
                   font=fonts['default'],
                   padding=(12, 8),
                   relief='flat',
                   focuscolor='none')

    style.map('Editor.Cancel.TButton',
              background=[('hover', '#6b7280'),
                         ('active', '#4b5563'),
                         ('pressed', '#4b5563'),
                         ('!active', '#9ca3af')],  # 灰色取消按钮
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', '#6b7280'),
                          ('active', '#4b5563'),
                          ('!active', '#9ca3af')])

    # 编辑器测试按钮 - 渐变蓝色
    style.configure('Editor.Test.TButton',
                   font=fonts['default'],
                   padding=(12, 8),
                   relief='flat',
                   focuscolor='none')

    style.map('Editor.Test.TButton',
              background=[('hover', '#2563eb'),
                         ('active', '#1d4ed8'),
                         ('pressed', '#1d4ed8'),
                         ('!active', '#3b82f6')],  # 蓝色测试按钮
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', '#2563eb'),
                          ('active', '#1d4ed8'),
                          ('!active', '#3b82f6')])

    # 编辑器快速时间按钮 - 渐变紫色
    style.configure('Editor.Quick.TButton',
                   font=fonts['default'],
                   padding=(8, 6),
                   relief='flat',
                   focuscolor='none')

    style.map('Editor.Quick.TButton',
              background=[('hover', '#7c3aed'),
                         ('active', '#6d28d9'),
                         ('pressed', '#6d28d9'),
                         ('!active', '#8b5cf6')],  # 紫色快速按钮
              foreground=[('hover', colors['text_white']),
                         ('active', colors['text_white']),
                         ('pressed', colors['text_white']),
                         ('!active', colors['text_white'])],
              bordercolor=[('hover', '#7c3aed'),
                          ('active', '#6d28d9'),
                          ('!active', '#8b5cf6')])

    return style


def create_notification_frame(parent, message, notification_type='info'):
    """创建内联通知框架"""
    colors = ModernTheme.COLORS
    
    # 选择颜色
    if notification_type == 'success':
        bg_color = colors['success_light']
        text_color = colors['success']
        icon = '✓'
    elif notification_type == 'error':
        bg_color = colors['error_light']
        text_color = colors['error']
        icon = '✗'
    elif notification_type == 'warning':
        bg_color = colors['warning_light']
        text_color = colors['warning']
        icon = '⚠'
    else:  # info
        bg_color = colors['info_light']
        text_color = colors['info']
        icon = 'ℹ'
    
    # 创建通知框架
    notification_frame = tk.Frame(parent, bg=bg_color, relief='solid', bd=1)
    
    # 图标标签 - 增大图标字体
    icon_label = tk.Label(notification_frame, text=icon,
                         bg=bg_color, fg='#000000',  # 黑色图标
                         font=('Segoe UI', 14, 'bold'))  # 更大的图标
    icon_label.pack(side='left', padx=(12, 8), pady=8)
    
    # 消息标签 - 使用更大的黑色字体
    message_label = tk.Label(notification_frame, text=message,
                           bg=bg_color, fg='#000000',  # 黑色字体
                           font=('Microsoft YaHei UI', 12, 'bold'),  # 更大的字体
                           wraplength=400, justify='left')
    message_label.pack(side='left', padx=(0, 12), pady=8, fill='x', expand=True)
    
    return notification_frame


def create_card_frame(parent, title=None, compact=False, **kwargs):
    """创建卡片样式的框架"""
    colors = ModernTheme.COLORS

    # 主框架
    card_frame = tk.Frame(parent, bg=colors['bg_primary'],
                         relief='solid', bd=1, **kwargs)

    if title:
        # 标题区域 - 紧凑版本
        title_height = 28 if compact else 40
        title_frame = tk.Frame(card_frame, bg=colors['bg_secondary'], height=title_height)
        title_frame.pack(fill='x', padx=0, pady=0)
        title_frame.pack_propagate(False)

        title_font = ModernTheme.FONTS['small'] if compact else ModernTheme.FONTS['subheading']
        title_label = tk.Label(title_frame, text=title,
                              bg=colors['bg_secondary'],
                              fg=colors['text_primary'],
                              font=title_font)

        title_pady = 6 if compact else 12
        title_padx = 8 if compact else 16
        title_label.pack(side='left', padx=title_padx, pady=title_pady)

        # 分隔线
        separator = tk.Frame(card_frame, bg=colors['border'], height=1)
        separator.pack(fill='x')

    # 内容区域 - 紧凑版本
    content_padx = 8 if compact else 16
    content_pady = 8 if compact else 16
    content_frame = tk.Frame(card_frame, bg=colors['bg_primary'])
    content_frame.pack(fill='both', expand=True, padx=content_padx, pady=content_pady)

    return card_frame, content_frame


def create_compact_card_frame(parent, title=None, **kwargs):
    """创建紧凑卡片样式的框架"""
    return create_card_frame(parent, title, compact=True, **kwargs)
