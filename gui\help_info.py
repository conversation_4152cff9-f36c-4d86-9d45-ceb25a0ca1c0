"""
帮助信息模块
提供使用说明和关于信息的显示功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from pathlib import Path
from typing import Optional


class HelpInfoManager:
    """帮助信息管理器"""
    
    def __init__(self, parent: tk.Tk):
        self.parent = parent
    
    def show_help(self):
        """显示使用说明"""
        help_window = self._create_help_window("使用说明", self._get_help_content())
        self._center_window(help_window, 800, 600)
    
    def show_about(self):
        """显示关于信息"""
        about_window = self._create_help_window("关于", self._get_about_content())
        self._center_window(about_window, 500, 400)
    
    def _create_help_window(self, title: str, content: str) -> tk.Toplevel:
        """创建帮助窗口"""
        window = tk.Toplevel(self.parent)
        window.title(title)
        window.transient(self.parent)
        window.grab_set()
        
        # 设置图标
        self._set_window_icon(window)
        
        # 创建滚动文本框
        text_frame = ttk.Frame(window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_widget = scrolledtext.ScrolledText(
            text_frame,
            wrap=tk.WORD,
            font=("Microsoft YaHei", 12),
            state=tk.DISABLED
        )
        text_widget.pack(fill=tk.BOTH, expand=True)
        
        # 插入内容
        text_widget.config(state=tk.NORMAL)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
        
        # 创建关闭按钮
        button_frame = ttk.Frame(window)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        close_button = ttk.Button(
            button_frame,
            text="关闭",
            command=window.destroy
        )
        close_button.pack(side=tk.RIGHT)
        
        # 绑定ESC键关闭
        window.bind('<Escape>', lambda e: window.destroy())
        
        return window
    
    def _set_window_icon(self, window: tk.Toplevel):
        """设置窗口图标"""
        try:
            icon_path = Path(__file__).parent.parent / "1.ico"
            if icon_path.exists():
                window.iconbitmap(str(icon_path))
        except Exception:
            pass
    
    def _center_window(self, window: tk.Toplevel, width: int, height: int):
        """居中显示窗口"""
        try:
            # 更新窗口以获取准确信息
            window.update_idletasks()
            
            # 获取屏幕尺寸
            screen_width = window.winfo_screenwidth()
            screen_height = window.winfo_screenheight()
            
            # 计算居中位置
            x = max(0, (screen_width - width) // 2)
            y = max(0, (screen_height - height) // 2)
            
            # 设置窗口位置和大小
            window.geometry(f"{width}x{height}+{x}+{y}")
            
        except Exception:
            # 如果居中失败，使用默认位置
            window.geometry(f"{width}x{height}")
    
    def _get_help_content(self) -> str:
        """获取使用说明内容"""
        return """Outlook邮件定时发送工具 v2.0 使用说明

═══════════════════════════════════════════════════════════════

📧 功能概述
本工具通过匹配Outlook草稿箱中的邮件进行定时发送，支持多任务管理和自动重试功能。

═══════════════════════════════════════════════════════════════

🚀 快速开始

1. 准备邮件
   • 在Outlook中编写邮件（收件人、主题、正文、附件等）
   • 将邮件保存到草稿箱（不要发送）
   • 记住邮件的主题，稍后需要用关键词匹配

2. 创建发送任务
   • 点击工具栏的"新建任务"按钮或使用快捷键 Ctrl+N
   • 填写任务名称（便于识别）
   • 输入邮件主题关键词（用于匹配草稿箱中的邮件）
   • 设置发送时间
   • 选择是否包含附件
   • 设置最大重试次数
   • 点击"保存"创建任务

3. 启动调度器
   • 点击"启动调度器"按钮开始自动监控
   • 程序会在设定时间自动发送匹配的草稿邮件
   • 可以随时点击"停止调度器"暂停

═══════════════════════════════════════════════════════════════

⚙️ 详细功能

任务管理：
• 支持多个邮件任务同时管理
• 任务状态实时显示（等待、运行、完成、失败、已取消、已暂停）
• 支持任务的编辑、删除、复制、暂停、恢复、重试操作
• 任务数据自动保存到配置文件

定时发送：
• 精确到秒的定时发送
• 通过邮件主题关键词匹配草稿箱中的邮件
• 自动重试失败的任务（可配置重试次数）
• 发送状态实时反馈和倒计时显示

邮件功能：
• 基于Outlook草稿箱的邮件发送
• 支持HTML格式邮件和纯文本邮件
• 支持邮件附件（可选择是否包含）
• 发送成功后可自动删除草稿（可配置）

系统功能：
• 最小化到系统托盘
• 单实例运行保护
• 退出确认提示
• 自动保存配置和历史记录

═══════════════════════════════════════════════════════════════

� 任务操作详解

创建任务：
• 任务名称：便于识别的任务描述
• 邮件主题关键词：用于匹配草稿箱中邮件的关键词
• 发送时间：可手动设置或使用快速时间按钮
• 包含附件：选择是否发送草稿邮件中的附件
• 最大重试次数：发送失败时的重试次数

快速时间设置：
• 1分钟后、5分钟后、10分钟后
• 30分钟后、1小时后、明天此时

任务状态说明：
• 等待：任务已创建，等待执行时间
• 运行：任务正在执行中
• 完成：任务执行成功
• 失败：任务执行失败，可重试
• 已取消：任务被用户取消
• 已暂停：任务被用户暂停

═══════════════════════════════════════════════════════════════

�🔧 快捷键和操作

快捷键：
• Ctrl+N：新建任务
• Ctrl+Q：退出程序
• F5：刷新任务列表
• ESC：关闭对话框

任务操作：
• 双击任务：编辑任务
• 右键菜单：编辑、删除、复制、暂停、恢复、重试
• 工具栏按钮：新建、编辑、删除、启动/停止调度器

═══════════════════════════════════════════════════════════════

⚠️ 重要注意事项

1. 邮件准备：
   • 必须先在Outlook中编写完整邮件并保存到草稿箱
   • 邮件主题关键词用于匹配草稿箱中的邮件
   • 确保草稿邮件的主题包含设置的关键词

2. 系统要求：
   • Windows操作系统
   • 已安装并配置Microsoft Outlook
   • Outlook必须处于登录状态

3. 使用建议：
   • 建议先手动发送一封测试邮件确认Outlook配置正确
   • 重要邮件建议提前设置发送时间以便处理异常
   • 定期检查任务状态确保正常执行

4. 故障排除：
   • 发送失败：检查Outlook是否正常运行和登录
   • 找不到邮件：确认草稿箱中有匹配关键词的邮件
   • 权限问题：确保Outlook允许外部程序访问

═══════════════════════════════════════════════════════════════

💡 使用技巧

• 复制任务：右键点击现有任务选择"复制"快速创建相似任务
• 批量管理：可以同时创建多个任务，调度器会自动按时间执行
• 系统托盘：最小化窗口时程序会隐藏到系统托盘继续运行
• 主题匹配：建议使用独特的关键词避免匹配到错误的草稿邮件
• 时间设置：使用快速时间按钮可以快速设置常用的发送时间
• 状态监控：任务列表会实时显示倒计时和执行状态
• 历史记录：程序会自动记录所有发送历史便于查看

═══════════════════════════════════════════════════════════════

📞 技术支持

如有问题或建议，请检查：
• 程序日志文件（如果启用）
• Outlook连接状态
• 草稿箱中的邮件

更多帮助请查看程序菜单中的"关于"信息。"""
    
    def _get_about_content(self) -> str:
        """获取关于信息内容"""
        return """Outlook邮件定时发送工具 v2.0

═══════════════════════════════════════════════════════════════

📋 软件信息

软件名称：Outlook邮件定时发送工具
版本号：v2.0
开发者：qq12626023
开发语言：Python 3.x
界面框架：Tkinter

═══════════════════════════════════════════════════════════════

✨ 主要特性

🔹 现代化界面设计
   • 简洁美观的用户界面
   • 响应式布局设计
   • 直观的操作体验

🔹 强大的任务管理
   • 多任务并发处理
   • 任务状态实时监控
   • 灵活的任务调度

🔹 可靠的邮件发送
   • 基于Outlook COM组件
   • 自动重试机制
   • 详细的状态反馈

🔹 贴心的系统集成
   • 系统托盘支持
   • 单实例运行保护
   • 自动保存配置

═══════════════════════════════════════════════════════════════

🛠️ 技术架构

核心组件：
• 任务调度器：负责任务的定时执行
• 邮件发送器：基于win32com与Outlook交互
• 界面管理器：提供现代化的用户界面
• 配置管理器：处理程序配置和数据存储

依赖库：
• pywin32：Windows COM组件支持
• pystray：系统托盘功能
• Pillow：图像处理支持
• psutil：系统信息获取

═══════════════════════════════════════════════════════════════


© 2024 Outlook邮件定时发送工具 v2.0
保留所有权利"""


# 创建全局实例供其他模块使用
def create_help_manager(parent: tk.Tk) -> HelpInfoManager:
    """创建帮助信息管理器实例"""
    return HelpInfoManager(parent)
