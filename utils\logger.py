"""
空日志模块
提供日志接口但不实际记录任何信息
"""


class NoOpLogger:
    """空操作日志记录器 - 不执行任何日志记录"""

    def debug(self, msg, *args, **kwargs):
        pass

    def info(self, msg, *args, **kwargs):
        pass

    def warning(self, msg, *args, **kwargs):
        pass

    def error(self, msg, *args, **kwargs):
        pass

    def critical(self, msg, *args, **kwargs):
        pass

    def exception(self, msg, *args, **kwargs):
        pass


class LoggerManager:
    """空日志管理器"""

    def __init__(self):
        self.logger = NoOpLogger()

    def setup_logger(self):
        """空设置方法"""
        pass

    def get_logger(self):
        """获取logger实例"""
        return self.logger

    def debug(self, message: str, *args, **kwargs):
        """空调试方法"""
        pass

    def info(self, message: str, *args, **kwargs):
        """空信息方法"""
        pass

    def warning(self, message: str, *args, **kwargs):
        """空警告方法"""
        pass

    def error(self, message: str, *args, **kwargs):
        """空错误方法"""
        pass

    def critical(self, message: str, *args, **kwargs):
        """空严重错误方法"""
        pass

    def exception(self, message: str, *args, **kwargs):
        """空异常方法"""
        pass


# 全局日志管理器实例
logger_manager = LoggerManager()
logger = logger_manager.get_logger()


def log_function_call(func):
    """空装饰器：不记录函数调用"""
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)
    return wrapper


def log_task_operation(operation: str):
    """空装饰器：不记录任务操作"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        return wrapper
    return decorator
