"""
UI组件模块
提供可复用的UI组件
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from typing import Optional, Callable, Any


class DateTimePicker:
    """日期时间选择器"""
    
    def __init__(self, parent, label_text: str = "选择时间:"):
        self.frame = ttk.LabelFrame(parent, text=label_text, padding=10)
        
        # 当前时间
        now = datetime.now()
        
        # 日期部分
        date_frame = ttk.Frame(self.frame)
        date_frame.pack(fill='x', pady=(0, 5))
        
        ttk.Label(date_frame, text="日期:").pack(side='left', padx=(0, 5))
        
        # 年
        self.year_var = tk.StringVar(value=str(now.year))
        self.year_spin = ttk.Spinbox(date_frame, from_=2023, to=2030, width=6, 
                                    textvariable=self.year_var)
        self.year_spin.pack(side='left', padx=2)
        ttk.Label(date_frame, text="年").pack(side='left', padx=(0, 5))
        
        # 月
        self.month_var = tk.StringVar(value=str(now.month))
        self.month_spin = ttk.Spinbox(date_frame, from_=1, to=12, width=4, 
                                     textvariable=self.month_var)
        self.month_spin.pack(side='left', padx=2)
        ttk.Label(date_frame, text="月").pack(side='left', padx=(0, 5))
        
        # 日
        self.day_var = tk.StringVar(value=str(now.day))
        self.day_spin = ttk.Spinbox(date_frame, from_=1, to=31, width=4, 
                                   textvariable=self.day_var)
        self.day_spin.pack(side='left', padx=2)
        ttk.Label(date_frame, text="日").pack(side='left')
        
        # 时间部分
        time_frame = ttk.Frame(self.frame)
        time_frame.pack(fill='x')
        
        ttk.Label(time_frame, text="时间:").pack(side='left', padx=(0, 5))
        
        # 时
        self.hour_var = tk.StringVar(value=str(now.hour))
        self.hour_spin = ttk.Spinbox(time_frame, from_=0, to=23, width=4, 
                                    textvariable=self.hour_var)
        self.hour_spin.pack(side='left', padx=2)
        ttk.Label(time_frame, text="时").pack(side='left', padx=(0, 5))
        
        # 分
        self.minute_var = tk.StringVar(value=str(now.minute))
        self.minute_spin = ttk.Spinbox(time_frame, from_=0, to=59, width=4, 
                                      textvariable=self.minute_var)
        self.minute_spin.pack(side='left', padx=2)
        ttk.Label(time_frame, text="分").pack(side='left', padx=(0, 5))
        
        # 秒
        self.second_var = tk.StringVar(value=str(now.second))
        self.second_spin = ttk.Spinbox(time_frame, from_=0, to=59, width=4, 
                                      textvariable=self.second_var)
        self.second_spin.pack(side='left', padx=2)
        ttk.Label(time_frame, text="秒").pack(side='left')
    
    def pack(self, **kwargs):
        """打包组件"""
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局"""
        self.frame.grid(**kwargs)
    
    def get_datetime(self) -> Optional[datetime]:
        """获取选择的日期时间"""
        try:
            year = int(self.year_var.get())
            month = int(self.month_var.get())
            day = int(self.day_var.get())
            hour = int(self.hour_var.get())
            minute = int(self.minute_var.get())
            second = int(self.second_var.get())
            
            return datetime(year, month, day, hour, minute, second)
        except ValueError:
            return None
    
    def set_datetime(self, dt: datetime):
        """设置日期时间"""
        self.year_var.set(str(dt.year))
        self.month_var.set(str(dt.month))
        self.day_var.set(str(dt.day))
        self.hour_var.set(str(dt.hour))
        self.minute_var.set(str(dt.minute))
        self.second_var.set(str(dt.second))


class StatusBar:
    """状态栏组件"""
    
    def __init__(self, parent):
        self.frame = ttk.Frame(parent)
        
        # 状态文本
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(self.frame, textvariable=self.status_var)
        self.status_label.pack(side='left', padx=5)
        
        # 分隔符
        ttk.Separator(self.frame, orient='vertical').pack(side='left', fill='y', padx=5)
        
        # 任务计数
        self.task_count_var = tk.StringVar(value="任务: 0")
        self.task_count_label = ttk.Label(self.frame, textvariable=self.task_count_var)
        self.task_count_label.pack(side='left', padx=5)
        
        # 分隔符
        ttk.Separator(self.frame, orient='vertical').pack(side='left', fill='y', padx=5)
        
        # 运行状态
        self.running_var = tk.StringVar(value="已停止")
        self.running_label = ttk.Label(self.frame, textvariable=self.running_var)
        self.running_label.pack(side='left', padx=5)
    
    def pack(self, **kwargs):
        """打包组件"""
        self.frame.pack(**kwargs)
    
    def set_status(self, text: str):
        """设置状态文本"""
        self.status_var.set(text)
    
    def set_task_count(self, count: int):
        """设置任务计数"""
        self.task_count_var.set(f"任务: {count}")
    
    def set_running_status(self, running: bool):
        """设置运行状态"""
        self.running_var.set("运行中" if running else "已停止")


class CountdownDisplay:
    """倒计时显示组件"""
    
    def __init__(self, parent):
        self.frame = ttk.LabelFrame(parent, text="倒计时", padding=10)
        
        # 倒计时文本
        self.countdown_var = tk.StringVar(value="无活动任务")
        self.countdown_label = ttk.Label(self.frame, textvariable=self.countdown_var, 
                                        font=('Arial', 12, 'bold'))
        self.countdown_label.pack()
    
    def pack(self, **kwargs):
        """打包组件"""
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局"""
        self.frame.grid(**kwargs)
    
    def update_countdown(self, text: str):
        """更新倒计时文本"""
        self.countdown_var.set(text)


class ProgressDialog:
    """进度对话框"""
    
    def __init__(self, parent, title: str = "处理中..."):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("300x120")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        # 消息标签
        self.message_var = tk.StringVar(value="正在处理...")
        message_label = ttk.Label(self.dialog, textvariable=self.message_var)
        message_label.pack(pady=10)
        
        # 进度条
        self.progress = ttk.Progressbar(self.dialog, mode='indeterminate')
        self.progress.pack(pady=10, padx=20, fill='x')
        self.progress.start()
        
        # 取消按钮
        self.cancelled = False
        cancel_btn = ttk.Button(self.dialog, text="取消", command=self.cancel)
        cancel_btn.pack(pady=5)
    
    def set_message(self, message: str):
        """设置消息"""
        self.message_var.set(message)
        self.dialog.update()
    
    def cancel(self):
        """取消操作"""
        self.cancelled = True
        self.close()
    
    def close(self):
        """关闭对话框"""
        self.progress.stop()
        self.dialog.destroy()
    
    def is_cancelled(self) -> bool:
        """检查是否被取消"""
        return self.cancelled


class ConfirmDialog:
    """确认对话框"""
    
    def __init__(self, parent, title: str, message: str, 
                 confirm_text: str = "确定", cancel_text: str = "取消"):
        self.result = False
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("350x150")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        # 消息
        message_label = ttk.Label(self.dialog, text=message, wraplength=300)
        message_label.pack(pady=20, padx=20)
        
        # 按钮框架
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(pady=10)
        
        # 确定按钮
        confirm_btn = ttk.Button(button_frame, text=confirm_text, 
                                command=self.confirm)
        confirm_btn.pack(side='left', padx=5)
        
        # 取消按钮
        cancel_btn = ttk.Button(button_frame, text=cancel_text, 
                               command=self.cancel)
        cancel_btn.pack(side='left', padx=5)
        
        # 绑定回车和ESC键
        self.dialog.bind('<Return>', lambda e: self.confirm())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
        
        # 设置焦点
        confirm_btn.focus_set()
    
    def confirm(self):
        """确认"""
        self.result = True
        self.dialog.destroy()
    
    def cancel(self):
        """取消"""
        self.result = False
        self.dialog.destroy()
    
    def show(self) -> bool:
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result


def show_error(parent, title: str, message: str):
    """显示错误消息"""
    messagebox.showerror(title, message, parent=parent)


def show_warning(parent, title: str, message: str):
    """显示警告消息"""
    messagebox.showwarning(title, message, parent=parent)


def show_info(parent, title: str, message: str):
    """显示信息消息"""
    messagebox.showinfo(title, message, parent=parent)


def ask_yes_no(parent, title: str, message: str) -> bool:
    """询问是否确认"""
    return messagebox.askyesno(title, message, parent=parent)


def format_time_remaining(seconds: float) -> str:
    """格式化剩余时间"""
    if seconds <= 0:
        return "即将执行"
    
    days = int(seconds // 86400)
    hours = int((seconds % 86400) // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    
    if days > 0:
        return f"{days}天 {hours:02d}:{minutes:02d}:{secs:02d}"
    else:
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"


def format_datetime(dt: datetime) -> str:
    """格式化日期时间"""
    return dt.strftime("%Y-%m-%d %H:%M:%S")
