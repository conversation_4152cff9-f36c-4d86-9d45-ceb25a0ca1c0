#!/usr/bin/env python3
"""
自动化打包脚本
用于构建优化的可执行文件包
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import time

def print_step(step_name):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"  {step_name}")
    print(f"{'='*60}")

def run_command(cmd, description):
    """运行命令并处理错误"""
    print(f"\n执行: {description}")
    print(f"命令: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def check_dependencies():
    """检查依赖"""
    print_step("检查依赖")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✓ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("✗ PyInstaller未安装")
        print("请运行: pip install pyinstaller")
        return False
    
    # 检查UPX（可选）
    try:
        result = subprocess.run(['upx', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ UPX可用 - 将启用压缩")
        else:
            print("! UPX不可用 - 跳过压缩")
    except FileNotFoundError:
        print("! UPX不可用 - 跳过压缩")
    
    return True

def clean_build():
    """清理构建目录"""
    print_step("清理构建目录")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"删除目录: {dir_name}")
            shutil.rmtree(dir_name)
        else:
            print(f"目录不存在: {dir_name}")

def install_minimal_deps():
    """安装最小依赖"""
    print_step("安装最小依赖")

    if os.path.exists('requirements_minimal.txt'):
        # 先尝试直接安装各个包，避免编码问题
        packages = [
            'pywin32>=305',
            'psutil>=5.9.0',
            'pystray>=0.19.4',
            'Pillow>=9.0.0',
            'pyinstaller>=5.0.0'
        ]

        print("逐个安装依赖包以避免编码问题...")
        for package in packages:
            print(f"安装: {package}")
            if not run_command(f'pip install "{package}"', f"安装 {package}"):
                print(f"警告: {package} 安装失败，继续安装其他包...")

        return True
    else:
        print("requirements_minimal.txt不存在，跳过依赖安装")
        return True

def build_package():
    """构建包"""
    print_step("构建可执行文件包")
    
    if not os.path.exists('outlook_scheduler.spec'):
        print("错误: outlook_scheduler.spec文件不存在")
        return False
    
    return run_command(
        'pyinstaller outlook_scheduler.spec --clean',
        "使用PyInstaller构建包"
    )

def optimize_package():
    """优化包"""
    print_step("优化包结构")
    
    dist_dir = Path('dist/OutlookScheduler')
    if not dist_dir.exists():
        print("错误: 构建目录不存在")
        return False
    
    # 创建必要的目录
    dirs_to_create = ['logs', 'config', 'backups', 'temp']
    for dir_name in dirs_to_create:
        dir_path = dist_dir / dir_name
        dir_path.mkdir(exist_ok=True)
        print(f"创建目录: {dir_name}")
    
    # 复制README文件
    if os.path.exists('README.md'):
        shutil.copy2('README.md', dist_dir / 'README.md')
        print("复制README.md")
    
    return True

def get_package_size():
    """获取包大小"""
    dist_dir = Path('dist/OutlookScheduler')
    if not dist_dir.exists():
        return 0
    
    total_size = 0
    for file_path in dist_dir.rglob('*'):
        if file_path.is_file():
            total_size += file_path.stat().st_size
    
    return total_size

def main():
    """主函数"""
    start_time = time.time()
    
    print("Outlook邮件定时发送工具 - 自动化打包脚本")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 清理构建目录
    clean_build()
    
    # 安装最小依赖
    if not install_minimal_deps():
        print("依赖安装失败")
        sys.exit(1)
    
    # 构建包
    if not build_package():
        print("构建失败")
        sys.exit(1)
    
    # 优化包
    if not optimize_package():
        print("优化失败")
        sys.exit(1)
    
    # 显示结果
    print_step("构建完成")
    
    package_size = get_package_size()
    package_size_mb = package_size / (1024 * 1024)
    
    elapsed_time = time.time() - start_time
    
    print(f"✓ 构建成功!")
    print(f"✓ 输出目录: dist/OutlookScheduler/")
    print(f"✓ 包大小: {package_size_mb:.1f} MB")
    print(f"✓ 构建时间: {elapsed_time:.1f} 秒")
    print(f"\n启动程序: dist/OutlookScheduler/OutlookScheduler.exe")

if __name__ == "__main__":
    main()
