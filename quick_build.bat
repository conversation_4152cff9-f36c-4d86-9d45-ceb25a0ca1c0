@echo off
chcp 65001 >nul
echo ================================================================
echo           Outlook邮件定时发送工具 - 快速打包脚本
echo ================================================================
echo.

echo [1/4] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请安装Python 3.7+并添加到系统PATH
    pause
    exit /b 1
)
echo ✅ Python环境正常

echo.
echo [2/4] 安装最小依赖...
echo 逐个安装依赖包以避免编码问题...

echo 安装 pywin32...
pip install "pywin32>=305"

echo 安装 psutil...
pip install "psutil>=5.9.0"

echo 安装 pystray...
pip install "pystray>=0.19.4"

echo 安装 Pillow...
pip install "Pillow>=9.0.0"

echo 安装 pyinstaller...
pip install "pyinstaller>=5.0.0"

echo ✅ 依赖安装完成

echo.
echo [3/4] 清理旧构建文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
echo ✅ 清理完成

echo.
echo [4/4] 开始打包...
if exist outlook_scheduler.spec (
    pyinstaller outlook_scheduler.spec --clean
    if errorlevel 1 (
        echo ❌ 打包失败
        pause
        exit /b 1
    )
) else (
    echo ❌ outlook_scheduler.spec文件不存在
    pause
    exit /b 1
)

echo.
echo ================================================================
echo                        打包完成！
echo ================================================================
echo.
echo 📁 输出目录: dist\OutlookScheduler\
echo 🚀 启动程序: dist\OutlookScheduler\OutlookScheduler.exe
echo.

if exist dist\OutlookScheduler (
    echo 📊 包大小统计:
    for /f %%i in ('powershell -command "(Get-ChildItem -Path 'dist\OutlookScheduler' -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB"') do echo    总大小: %%i MB
    echo.
    echo 🎉 打包成功！可以在目标机器上运行了。
) else (
    echo ❌ 打包目录不存在，可能打包失败
)

echo.
pause
