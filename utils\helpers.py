"""
辅助工具模块
提供各种实用工具函数
"""

import os
import sys
import json
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional


def ensure_directory(path: str) -> Path:
    """确保目录存在"""
    dir_path = Path(path)
    dir_path.mkdir(parents=True, exist_ok=True)
    return dir_path


def safe_json_load(file_path: str, default: Any = None) -> Any:
    """安全加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError, Exception):
        return default


def safe_json_save(file_path: str, data: Any) -> bool:
    """安全保存JSON文件"""
    try:
        # 确保目录存在
        ensure_directory(os.path.dirname(file_path))
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception:
        return False


def backup_file(file_path: str, backup_dir: str = "backups") -> Optional[str]:
    """备份文件"""
    try:
        if not os.path.exists(file_path):
            return None
        
        # 创建备份目录
        backup_path = ensure_directory(backup_dir)
        
        # 生成备份文件名
        file_name = os.path.basename(file_path)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file_name = f"{timestamp}_{file_name}"
        backup_file_path = backup_path / backup_file_name
        
        # 复制文件
        shutil.copy2(file_path, backup_file_path)
        return str(backup_file_path)
        
    except Exception:
        return None


def cleanup_old_backups(backup_dir: str = "backups", days: int = 7) -> int:
    """清理旧备份文件"""
    try:
        backup_path = Path(backup_dir)
        if not backup_path.exists():
            return 0
        
        cutoff_date = datetime.now() - timedelta(days=days)
        removed_count = 0
        
        for file_path in backup_path.iterdir():
            if file_path.is_file():
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_date:
                    file_path.unlink()
                    removed_count += 1
        
        return removed_count
        
    except Exception:
        return 0


def get_app_data_dir() -> Path:
    """获取应用数据目录"""
    if sys.platform == "win32":
        app_data = os.environ.get('APPDATA', os.path.expanduser('~'))
        return Path(app_data) / "OutlookScheduler"
    else:
        return Path.home() / ".outlook_scheduler"


def get_temp_dir() -> Path:
    """获取临时目录"""
    import tempfile
    return Path(tempfile.gettempdir()) / "outlook_scheduler"


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"


def validate_email_address(email: str) -> bool:
    """验证邮箱地址格式"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def sanitize_filename(filename: str) -> str:
    """清理文件名，移除非法字符"""
    import re
    # 移除或替换非法字符
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 移除前后空格和点
    sanitized = sanitized.strip(' .')
    # 限制长度
    if len(sanitized) > 255:
        sanitized = sanitized[:255]
    return sanitized


def get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    import platform
    import psutil
    
    try:
        return {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total,
            "memory_available": psutil.virtual_memory().available,
            "disk_usage": psutil.disk_usage('/').percent if sys.platform != "win32" else psutil.disk_usage('C:').percent
        }
    except Exception:
        return {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "error": "无法获取详细系统信息"
        }


def check_outlook_installed() -> bool:
    """检查Outlook是否已安装"""
    try:
        import win32com.client
        outlook = win32com.client.Dispatch("Outlook.Application")
        return True
    except Exception:
        return False


def get_outlook_version() -> Optional[str]:
    """获取Outlook版本"""
    try:
        import win32com.client
        outlook = win32com.client.Dispatch("Outlook.Application")
        return outlook.Version
    except Exception:
        return None


def is_admin() -> bool:
    """检查是否以管理员权限运行"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except Exception:
        return False


def restart_as_admin():
    """以管理员权限重启程序"""
    try:
        import ctypes
        import sys
        
        if is_admin():
            return False
        
        # 重新启动程序
        ctypes.windll.shell32.ShellExecuteW(
            None, "runas", sys.executable, " ".join(sys.argv), None, 1
        )
        return True
    except Exception:
        return False


def create_desktop_shortcut(name: str, target: str, icon: Optional[str] = None) -> bool:
    """创建桌面快捷方式"""
    try:
        import win32com.client
        
        desktop = os.path.join(os.path.expanduser('~'), 'Desktop')
        shortcut_path = os.path.join(desktop, f"{name}.lnk")
        
        shell = win32com.client.Dispatch("WScript.Shell")
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = os.path.dirname(target)
        
        if icon:
            shortcut.IconLocation = icon
        
        shortcut.save()
        return True
        
    except Exception:
        return False


def get_available_port(start_port: int = 8000, max_attempts: int = 100) -> Optional[int]:
    """获取可用端口"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    return None


def hash_string(text: str, algorithm: str = 'md5') -> str:
    """计算字符串哈希值"""
    import hashlib
    
    hash_obj = hashlib.new(algorithm)
    hash_obj.update(text.encode('utf-8'))
    return hash_obj.hexdigest()


def compress_data(data: bytes) -> bytes:
    """压缩数据"""
    import gzip
    return gzip.compress(data)


def decompress_data(compressed_data: bytes) -> bytes:
    """解压数据"""
    import gzip
    return gzip.decompress(compressed_data)


def retry_on_exception(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        import time
                        time.sleep(delay * (attempt + 1))
                    else:
                        raise last_exception
            
            return None
        return wrapper
    return decorator


def measure_execution_time(func):
    """测量函数执行时间的装饰器"""
    def wrapper(*args, **kwargs):
        import time
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"函数 {func.__name__} 执行时间: {execution_time:.4f} 秒")
        return result
    return wrapper
