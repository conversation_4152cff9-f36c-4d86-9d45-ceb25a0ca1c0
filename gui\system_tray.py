"""
系统托盘管理模块
提供系统托盘功能，支持窗口最小化到托盘
"""

import threading
import tkinter as tk
from pathlib import Path
from typing import Optional, Callable

try:
    import pystray
    from PIL import Image, ImageDraw
    TRAY_AVAILABLE = True
except ImportError:
    TRAY_AVAILABLE = False
    pystray = None
    Image = None
    ImageDraw = None

from utils.logger import logger


class SystemTrayManager:
    """系统托盘管理器"""
    
    def __init__(self, root: tk.Tk, app_name: str = "Outlook邮件定时发送工具"):
        self.root = root
        self.app_name = app_name
        self.tray_icon = None
        self.is_hidden = False
        self.on_exit_callback: Optional[Callable] = None
        
        # 检查托盘功能是否可用
        if not TRAY_AVAILABLE:
            logger.warning("系统托盘功能不可用：缺少pystray或Pillow依赖")
            return
        
        # 创建托盘图标
        self.create_tray_icon()
        
        # 绑定窗口事件
        self.setup_window_events()
        
        logger.info("系统托盘管理器初始化完成")
    
    def is_available(self):
        """检查托盘功能是否可用"""
        return TRAY_AVAILABLE and self.tray_icon is not None
    
    def create_tray_icon(self):
        """创建系统托盘图标"""
        try:
            # 尝试加载项目图标
            icon_path = Path(__file__).parent.parent / "1.ico"
            
            if icon_path.exists():
                try:
                    # 加载ICO文件
                    image = Image.open(icon_path)
                    # 确保图标大小合适
                    image = image.resize((64, 64), Image.Resampling.LANCZOS)
                    logger.info(f"成功加载托盘图标: {icon_path}")
                except Exception as e:
                    logger.warning(f"加载图标文件失败: {e}，使用默认图标")
                    image = self.create_default_icon()
            else:
                logger.warning(f"图标文件不存在: {icon_path}，使用默认图标")
                image = self.create_default_icon()
            
            # 创建托盘菜单
            menu = self.create_tray_menu()
            
            # 创建托盘图标
            self.tray_icon = pystray.Icon(
                name=self.app_name,
                icon=image,
                title=self.app_name,
                menu=menu
            )
            
            # 设置双击事件
            self.tray_icon.default_action = self.on_tray_double_click
            
        except Exception as e:
            logger.error(f"创建系统托盘图标失败: {e}")
            self.tray_icon = None
    
    def create_default_icon(self):
        """创建默认托盘图标"""
        try:
            # 创建一个简单的默认图标
            width = height = 64
            image = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(image)
            
            # 绘制一个简单的邮件图标
            # 外框
            draw.rectangle([8, 16, 56, 48], outline=(70, 130, 180), width=2, fill=(240, 248, 255))
            # 信封折线
            draw.line([8, 16, 32, 32], fill=(70, 130, 180), width=2)
            draw.line([32, 32, 56, 16], fill=(70, 130, 180), width=2)
            
            return image
            
        except Exception as e:
            logger.error(f"创建默认图标失败: {e}")
            # 如果连默认图标都创建失败，返回一个纯色图标
            return Image.new('RGBA', (64, 64), (70, 130, 180, 255))
    
    def create_tray_menu(self):
        """创建托盘右键菜单"""
        try:
            menu_items = [
                pystray.MenuItem(
                    "显示窗口",
                    self.show_window,
                    default=True,
                    visible=lambda item: self.is_hidden
                ),
                pystray.MenuItem(
                    "隐藏窗口",
                    self.hide_window,
                    visible=lambda item: not self.is_hidden
                ),
                pystray.MenuItem("---", None),  # 分隔线
                pystray.MenuItem("退出程序", self.quit_application)
            ]
            
            return pystray.Menu(*menu_items)
            
        except Exception as e:
            logger.error(f"创建托盘菜单失败: {e}")
            return None
    
    def setup_window_events(self):
        """设置窗口事件绑定"""
        try:
            # 绑定窗口状态变化事件
            self.root.bind('<Unmap>', self.on_window_unmap)
            self.root.bind('<Map>', self.on_window_map)
            
            # 绑定窗口最小化事件
            self.root.bind('<FocusOut>', self.check_window_state)
            
            # 定期检查窗口状态
            self.check_window_state_periodically()
            
        except Exception as e:
            logger.error(f"设置窗口事件失败: {e}")
    
    def check_window_state_periodically(self):
        """定期检查窗口状态"""
        try:
            self.check_window_state()
            # 每500毫秒检查一次
            self.root.after(500, self.check_window_state_periodically)
        except Exception as e:
            logger.error(f"检查窗口状态失败: {e}")
    
    def check_window_state(self, event=None):
        """检查窗口状态"""
        try:
            if not self.is_available():
                return
            
            # 检查窗口是否最小化
            if self.root.state() == 'iconic':
                if not self.is_hidden:
                    self.hide_to_tray()
            elif self.root.state() == 'normal':
                if self.is_hidden:
                    self.is_hidden = False
                    
        except Exception as e:
            logger.debug(f"检查窗口状态时出错: {e}")
    
    def on_window_unmap(self, event=None):
        """窗口取消映射事件"""
        if event and event.widget == self.root:
            # 延迟检查，因为最小化时会触发unmap
            self.root.after(100, self.check_if_minimized)
    
    def on_window_map(self, event=None):
        """窗口映射事件"""
        if event and event.widget == self.root and self.is_hidden:
            self.is_hidden = False
    
    def check_if_minimized(self):
        """检查是否被最小化"""
        try:
            if self.root.state() == 'iconic':
                self.hide_to_tray()
        except Exception as e:
            logger.debug(f"检查最小化状态失败: {e}")
    
    def hide_to_tray(self):
        """隐藏窗口到系统托盘"""
        try:
            if not self.is_available():
                logger.warning("系统托盘不可用，无法隐藏到托盘")
                return
            
            # 隐藏窗口
            self.root.withdraw()
            self.is_hidden = True
            
            # 启动托盘图标（如果还没启动）
            if not hasattr(self, '_tray_thread') or not self._tray_thread.is_alive():
                self.start_tray()
            
            logger.info("窗口已隐藏到系统托盘")
            
        except Exception as e:
            logger.error(f"隐藏到系统托盘失败: {e}")
    
    def start_tray(self):
        """启动系统托盘"""
        try:
            if not self.is_available():
                return
            
            # 在单独线程中运行托盘
            self._tray_thread = threading.Thread(
                target=self.tray_icon.run,
                daemon=True,
                name="SystemTray"
            )
            self._tray_thread.start()
            
            logger.info("系统托盘已启动")
            
        except Exception as e:
            logger.error(f"启动系统托盘失败: {e}")
    
    def show_window(self, icon=None, item=None):
        """显示窗口"""
        try:
            # 使用after方法确保在主线程中执行
            self.root.after(0, self._show_window_main_thread)
            
        except Exception as e:
            logger.error(f"显示窗口失败: {e}")
    
    def _show_window_main_thread(self):
        """在主线程中显示窗口"""
        try:
            self.root.deiconify()  # 取消最小化
            self.root.lift()       # 置于前台
            self.root.focus_force()  # 强制获取焦点
            self.is_hidden = False
            
            logger.info("窗口已从系统托盘恢复")
            
        except Exception as e:
            logger.error(f"在主线程中显示窗口失败: {e}")
    
    def hide_window(self, icon=None, item=None):
        """隐藏窗口"""
        try:
            self.root.after(0, self.hide_to_tray)
        except Exception as e:
            logger.error(f"隐藏窗口失败: {e}")
    
    def on_tray_double_click(self, icon, item):
        """托盘图标双击事件"""
        if self.is_hidden:
            self.show_window()
        else:
            self.hide_window()
    
    def quit_application(self, icon=None, item=None):
        """退出应用程序"""
        try:
            # 停止托盘图标
            if self.tray_icon:
                self.tray_icon.stop()
            
            # 调用退出回调
            if self.on_exit_callback:
                self.root.after(0, self.on_exit_callback)
            else:
                self.root.after(0, self.root.quit)
                
        except Exception as e:
            logger.error(f"退出应用程序失败: {e}")
    
    def set_exit_callback(self, callback: Callable):
        """设置退出回调函数"""
        self.on_exit_callback = callback
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.tray_icon:
                self.tray_icon.stop()
                logger.info("系统托盘已清理")
        except Exception as e:
            logger.error(f"清理系统托盘失败: {e}")
