"""
现代化任务管理界面
重新设计的任务列表和管理功能
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
from typing import List, Optional, Callable, Dict, Any

from scheduler.task_model import EmailTask, TaskStatus
from gui.modern_theme import ModernTheme, create_card_frame, create_compact_card_frame, create_notification_frame
from gui.components import format_time_remaining, format_datetime, ask_yes_no
from gui.task_editor import show_task_editor
from utils.logger import logger


class ModernTaskManagerFrame:
    """现代化任务管理框架"""
    
    def __init__(self, parent):
        self.parent = parent
        self.tasks: Dict[str, EmailTask] = {}
        self.item_to_task: Dict[str, str] = {}
        self.colors = ModernTheme.COLORS
        self.fonts = ModernTheme.FONTS
        
        # 回调函数
        self.on_task_add: Optional[Callable[[EmailTask], None]] = None
        self.on_task_update: Optional[Callable[[EmailTask], None]] = None
        self.on_task_delete: Optional[Callable[[str], None]] = None
        self.on_task_pause: Optional[Callable[[str], None]] = None
        self.on_task_resume: Optional[Callable[[str], None]] = None
        self.on_task_cancel: Optional[Callable[[str], None]] = None
        self.on_task_retry: Optional[Callable[[str], None]] = None
        
        # 创建界面
        self.create_widgets()
        
        # 启动更新定时器
        self.update_timer()
    
    def create_widgets(self):
        """创建现代化界面组件"""
        # 主框架
        self.main_frame = tk.Frame(self.parent, bg=self.colors['bg_primary'])
        
        # 创建左右分栏布局
        self.create_layout()
        
        # 创建任务列表
        self.create_task_list()
        
        # 创建侧边栏
        self.create_sidebar()
        
        # 创建右键菜单
        self.create_context_menu()
    
    def create_layout(self):
        """创建布局"""
        # 主要内容区域（任务列表）- 紧凑版本
        self.content_frame = tk.Frame(self.main_frame, bg=self.colors['bg_primary'])
        self.content_frame.pack(side='left', fill='both', expand=True, padx=(0, 4))

        # 侧边栏 - 紧凑版本
        self.sidebar_frame = tk.Frame(self.main_frame, bg=self.colors['bg_primary'], width=200)
        self.sidebar_frame.pack(side='right', fill='y')
        self.sidebar_frame.pack_propagate(False)
    
    def create_task_list(self):
        """创建现代化任务列表"""
        # 任务列表卡片 - 紧凑版本
        list_card, list_content = create_compact_card_frame(self.content_frame, "任务列表")
        list_card.pack(fill='both', expand=True)
        
        # 工具栏 - 紧凑版本
        toolbar = tk.Frame(list_content, bg=self.colors['bg_primary'])
        toolbar.pack(fill='x', pady=(0, 6))

        # 左侧按钮
        left_buttons = tk.Frame(toolbar, bg=self.colors['bg_primary'])
        left_buttons.pack(side='left')

        ttk.Button(left_buttons, text="➕ 新建", style='Compact.Primary.TButton',
                  command=self.add_task).pack(side='left', padx=(0, 4))
        ttk.Button(left_buttons, text="✏️ 编辑", style='Compact.Secondary.TButton',
                  command=self.edit_task).pack(side='left', padx=(0, 4))
        ttk.Button(left_buttons, text="🗑️ 删除", style='Compact.Danger.TButton',
                  command=self.delete_task).pack(side='left', padx=(0, 4))

        # 右侧按钮
        right_buttons = tk.Frame(toolbar, bg=self.colors['bg_primary'])
        right_buttons.pack(side='right')

        ttk.Button(right_buttons, text="🔄 刷新", style='Compact.Secondary.TButton',
                  command=self.refresh_tasks).pack(side='right')
        
        # 创建Treeview - 紧凑版本
        tree_frame = tk.Frame(list_content, bg=self.colors['bg_primary'])
        tree_frame.pack(fill='both', expand=True)

        # 列定义
        columns = ('name', 'subject', 'scheduled_time', 'status', 'remaining')
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings',
                                style='Compact.Treeview', height=10)
        
        # 设置列标题和宽度 - 紧凑版本
        column_configs = [
            ('name', '任务名称', 120),
            ('subject', '邮件主题', 140),
            ('scheduled_time', '发送时间', 100),
            ('status', '状态', 60),
            ('remaining', '剩余', 80)
        ]
        
        for col_id, heading, width in column_configs:
            self.tree.heading(col_id, text=heading)
            self.tree.column(col_id, width=width, minwidth=80)
        
        # 滚动条
        v_scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient='horizontal', command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定事件
        self.tree.bind('<Double-1>', lambda e: self.edit_task())
        self.tree.bind('<Button-3>', self.show_context_menu)
        
        # 配置任务状态颜色
        self.configure_task_colors()
    
    def configure_task_colors(self):
        """配置任务状态颜色"""
        # 等待中 - 黄色背景
        self.tree.tag_configure('pending', background='#fef3c7', foreground='#92400e')
        # 运行中 - 紫色背景
        self.tree.tag_configure('running', background="#ef0cf7", foreground="#f8f8fa")
        # 已完成 - 绿色背景黑色字体（强制设置）
        self.tree.tag_configure('completed', background="#267a45", foreground='#000000')
        # 失败 - 红色背景白色字体（强制设置）
        self.tree.tag_configure('failed', background='#ef4444', foreground="#070707")
        # 已取消 - 灰色背景
        self.tree.tag_configure('cancelled', background="#9cce11", foreground='#374151')
        # 已暂停 - 紫色背景
        self.tree.tag_configure('paused', background="#ef0ef7", foreground='#3730a3')

        # 确保颜色配置立即生效
        self.tree.update_idletasks()
    
    def create_sidebar(self):
        """创建侧边栏"""
        # 倒计时卡片 - 紧凑版本
        countdown_card, countdown_content = create_compact_card_frame(self.sidebar_frame, "下一个任务")
        countdown_card.pack(fill='x', pady=(0, 4))

        self.countdown_var = tk.StringVar(value="无待执行任务")
        countdown_label = tk.Label(countdown_content, textvariable=self.countdown_var,
                                 bg=self.colors['bg_primary'],
                                 fg=self.colors['text_primary'],
                                 font=self.fonts['default'],
                                 wraplength=180, justify='center')
        countdown_label.pack(pady=4)

        # 快速操作卡片 - 紧凑版本
        actions_card, actions_content = create_compact_card_frame(self.sidebar_frame, "快速操作")
        actions_card.pack(fill='x', pady=(0, 4))

        # 操作按钮
        action_buttons = [
            ("⏸️ 暂停", self.pause_task, 'Compact.Warning.TButton'),
            ("▶️ 恢复", self.resume_task, 'Compact.Success.TButton'),
            ("❌ 取消", self.cancel_task, 'Compact.Danger.TButton'),
            ("🔄 重试", self.retry_task, 'Compact.Primary.TButton'),
            ("📋 复制", self.copy_task, 'Compact.Secondary.TButton'),
        ]

        for text, command, style in action_buttons:
            btn = ttk.Button(actions_content, text=text, style=style,
                           command=command)
            btn.pack(fill='x', pady=1)
        
        # 统计信息卡片 - 紧凑版本
        stats_card, stats_content = create_compact_card_frame(self.sidebar_frame, "统计信息")
        stats_card.pack(fill='x')
        
        self.stats_frame = stats_content
        self.update_statistics()
    
    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.parent, tearoff=0,
                                   bg=self.colors['bg_primary'],
                                   fg=self.colors['text_primary'],
                                   activebackground=self.colors['primary_light'],
                                   activeforeground=self.colors['text_white'],
                                   font=('Microsoft YaHei UI', 12))  # 增大右键菜单字体
        
        # 使用固定宽度格式确保对齐
        menu_items = [
            ("📝    编辑任务", self.edit_task),
            ("🗑    删除任务", self.delete_task),
            None,  # 分隔符
            ("⏸    暂停任务", self.pause_task),
            ("▶    恢复任务", self.resume_task),
            ("❌    取消任务", self.cancel_task),
            ("🔄    重试任务", self.retry_task),
            None,  # 分隔符
            ("📋    复制任务", self.copy_task),
        ]
        
        for item in menu_items:
            if item is None:
                self.context_menu.add_separator()
            else:
                text, command = item
                self.context_menu.add_command(label=text, command=command)
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        # 选择点击的项目
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            
            # 根据任务状态启用/禁用菜单项
            task = self.get_selected_task()
            if task:
                self.update_context_menu_state(task)
                self.context_menu.post(event.x_root, event.y_root)
    
    def update_context_menu_state(self, task: EmailTask):
        """更新右键菜单状态"""
        # 根据任务状态启用/禁用菜单项
        can_edit = task.status in [TaskStatus.PENDING, TaskStatus.PAUSED, TaskStatus.FAILED]
        can_pause = task.status == TaskStatus.PENDING
        can_resume = task.status == TaskStatus.PAUSED
        can_cancel = task.status in [TaskStatus.PENDING, TaskStatus.PAUSED, TaskStatus.RUNNING]
        can_retry = task.status == TaskStatus.FAILED and task.can_retry()
        
        # 更新菜单项状态（这里简化处理，实际可以更精细）
        pass
    
    def pack(self, **kwargs):
        """打包组件"""
        self.main_frame.pack(**kwargs)
    
    def get_selected_task(self) -> Optional[EmailTask]:
        """获取选中的任务"""
        selection = self.tree.selection()
        if not selection:
            return None

        item = selection[0]
        task_id = self.item_to_task.get(item)
        return self.tasks.get(task_id) if task_id else None

    def add_task(self):
        """添加任务"""
        task = show_task_editor(self.parent)
        if task and self.on_task_add:
            self.on_task_add(task)

    def edit_task(self):
        """编辑任务"""
        task = self.get_selected_task()
        if not task:
            return

        if task.status not in [TaskStatus.PENDING, TaskStatus.PAUSED, TaskStatus.FAILED]:
            return

        edited_task = show_task_editor(self.parent, task)
        if edited_task and self.on_task_update:
            self.on_task_update(edited_task)

    def delete_task(self):
        """删除任务"""
        task = self.get_selected_task()
        if not task:
            return

        if ask_yes_no(self.parent, "确认删除", f"确定要删除任务 '{task.name}' 吗？"):
            if self.on_task_delete:
                self.on_task_delete(task.id)

    def pause_task(self):
        """暂停任务"""
        task = self.get_selected_task()
        if not task or task.status != TaskStatus.PENDING:
            return

        if self.on_task_pause:
            self.on_task_pause(task.id)

    def resume_task(self):
        """恢复任务"""
        task = self.get_selected_task()
        if not task or task.status != TaskStatus.PAUSED:
            return

        if self.on_task_resume:
            self.on_task_resume(task.id)

    def cancel_task(self):
        """取消任务"""
        task = self.get_selected_task()
        if not task:
            return

        if task.status not in [TaskStatus.PENDING, TaskStatus.PAUSED, TaskStatus.RUNNING]:
            return

        if ask_yes_no(self.parent, "确认取消", f"确定要取消任务 '{task.name}' 吗？"):
            if self.on_task_cancel:
                self.on_task_cancel(task.id)

    def retry_task(self):
        """重试任务"""
        task = self.get_selected_task()
        if not task or not task.can_retry():
            return

        if self.on_task_retry:
            self.on_task_retry(task.id)

    def copy_task(self):
        """复制任务"""
        task = self.get_selected_task()
        if not task:
            return

        # 创建新任务（复制现有任务的设置）
        new_task = EmailTask.create_new(
            name=f"{task.name} - 副本",
            subject_keyword=task.subject_keyword,
            scheduled_time=datetime.now(),
            include_attachments=task.include_attachments
        )
        new_task.max_retries = task.max_retries

        # 打开编辑器
        edited_task = show_task_editor(self.parent, new_task)
        if edited_task and self.on_task_add:
            self.on_task_add(edited_task)

    def refresh_tasks(self):
        """刷新任务列表"""
        self.force_update_list()

    def update_tasks(self, tasks: List[EmailTask]):
        """更新任务数据"""
        self.tasks = {task.id: task for task in tasks}
        self.force_update_list()

    def force_update_list(self):
        """强制更新列表"""
        # 重新配置颜色以确保状态颜色正确显示
        self.configure_task_colors()
        self.update_task_list()

    def update_task_list(self):
        """更新任务列表显示"""
        # 保存当前选择的任务ID
        selected_task_id = None
        selection = self.tree.selection()
        if selection:
            selected_task_id = self.item_to_task.get(selection[0])

        # 保存滚动位置
        try:
            scroll_top = self.tree.yview()[0]
        except:
            scroll_top = 0

        # 清空现有项目和映射
        for item in self.tree.get_children():
            self.tree.delete(item)
        self.item_to_task.clear()

        # 添加任务项目
        selected_item = None
        for task in self.tasks.values():
            remaining = task.get_time_remaining()
            remaining_text = format_time_remaining(remaining) if remaining is not None else "N/A"

            # 根据状态设置标签
            tags = []
            if task.status == TaskStatus.PENDING:
                tags.append('pending')
            elif task.status == TaskStatus.RUNNING:
                tags.append('running')
            elif task.status == TaskStatus.COMPLETED:
                tags.append('completed')
            elif task.status == TaskStatus.FAILED:
                tags.append('failed')
            elif task.status == TaskStatus.CANCELLED:
                tags.append('cancelled')
            elif task.status == TaskStatus.PAUSED:
                tags.append('paused')

            item_id = self.tree.insert('', 'end', values=(
                task.name,
                task.subject_keyword,
                format_datetime(task.scheduled_time),
                task.get_status_display(),
                remaining_text
            ), tags=tags)

            # 存储任务ID到item的映射
            self.item_to_task[item_id] = task.id

            # 记录之前选中的项目
            if task.id == selected_task_id:
                selected_item = item_id

        # 恢复选择状态
        if selected_item:
            self.tree.selection_set(selected_item)
            self.tree.focus(selected_item)

        # 恢复滚动位置
        try:
            self.tree.yview_moveto(scroll_top)
        except:
            pass

        # 更新统计信息
        self.update_statistics()

    def update_countdown(self):
        """更新倒计时显示"""
        # 找到最近的待执行任务
        next_task = None
        min_remaining = float('inf')

        for task in self.tasks.values():
            if task.status in [TaskStatus.PENDING, TaskStatus.PAUSED]:
                remaining = task.get_time_remaining()
                if remaining is not None and remaining < min_remaining:
                    min_remaining = remaining
                    next_task = task

        if next_task:
            remaining_text = format_time_remaining(min_remaining)
            countdown_text = f"{next_task.name}\n剩余: {remaining_text}"
        else:
            countdown_text = "无待执行任务"

        self.countdown_var.set(countdown_text)

        # 同时更新列表中的剩余时间列
        self.update_remaining_times()

    def update_remaining_times(self):
        """只更新剩余时间列"""
        try:
            for item_id, task_id in self.item_to_task.items():
                task = self.tasks.get(task_id)
                if task:
                    remaining = task.get_time_remaining()
                    remaining_text = format_time_remaining(remaining) if remaining is not None else "N/A"

                    # 获取当前值
                    current_values = list(self.tree.item(item_id)['values'])
                    if len(current_values) >= 5:
                        # 只更新剩余时间列（第5列，索引4）
                        if current_values[4] != remaining_text:
                            current_values[4] = remaining_text
                            self.tree.item(item_id, values=current_values)
        except Exception:
            # 如果更新失败，忽略错误
            pass

    def update_statistics(self):
        """更新统计信息"""
        # 清空统计框架
        for widget in self.stats_frame.winfo_children():
            widget.destroy()

        # 统计各状态任务数量
        stats = {
            'total': len(self.tasks),
            'pending': 0,
            'running': 0,
            'completed': 0,
            'failed': 0,
            'cancelled': 0,
            'paused': 0
        }

        for task in self.tasks.values():
            if task.status == TaskStatus.PENDING:
                stats['pending'] += 1
            elif task.status == TaskStatus.RUNNING:
                stats['running'] += 1
            elif task.status == TaskStatus.COMPLETED:
                stats['completed'] += 1
            elif task.status == TaskStatus.FAILED:
                stats['failed'] += 1
            elif task.status == TaskStatus.CANCELLED:
                stats['cancelled'] += 1
            elif task.status == TaskStatus.PAUSED:
                stats['paused'] += 1

        # 显示统计信息
        stat_items = [
            ("总任务", stats['total'], self.colors['text_primary']),
            ("等待中", stats['pending'], self.colors['task_pending']),
            ("运行中", stats['running'], self.colors['task_running']),
            ("已完成", stats['completed'], self.colors['task_completed']),
            ("失败", stats['failed'], self.colors['task_failed']),
            ("已暂停", stats['paused'], self.colors['task_paused']),
        ]

        for i, (label, count, color) in enumerate(stat_items):
            if count > 0 or label == "总任务":
                stat_frame = tk.Frame(self.stats_frame, bg=self.colors['bg_primary'])
                stat_frame.pack(fill='x', pady=2)

                tk.Label(stat_frame, text=label,
                        bg=self.colors['bg_primary'],
                        fg=self.colors['text_secondary'],
                        font=self.fonts['default']).pack(side='left')

                tk.Label(stat_frame, text=str(count),
                        bg=self.colors['bg_primary'],
                        fg=color,
                        font=self.fonts['subheading']).pack(side='right')

    def update_timer(self):
        """定时更新"""
        # 每秒更新倒计时
        self.update_countdown()

        # 每秒检查一次
        self.parent.after(1000, self.update_timer)
