"""
现代化主窗口界面
应用程序的主界面 - 重新设计版本
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Optional
from pathlib import Path

from gui.modern_task_manager import ModernTaskManagerFrame
from gui.modern_theme import ModernTheme, configure_modern_style, create_card_frame, create_compact_card_frame
from gui.notification_system import NotificationManager, StatusDisplay, ProgressIndicator
from gui.system_tray import SystemTrayManager
from gui.help_info import create_help_manager
from scheduler.task_scheduler import task_scheduler
from scheduler.task_model import EmailTask, TaskStatus
from config.settings import config_manager
from utils.logger import logger


class ModernMainWindow:
    """现代化主窗口类"""

    def __init__(self):
        self.root = tk.Tk()
        self.colors = ModernTheme.COLORS
        self.fonts = ModernTheme.FONTS

        # 配置DPI感知和字体渲染
        self.configure_display_settings()

        # 配置现代化样式
        self.style = configure_modern_style()

        self.setup_window()
        self.create_widgets()
        self.setup_scheduler_callbacks()
        self.load_initial_data()

        # 初始化系统托盘
        self.setup_system_tray()

        # 初始化帮助信息管理器
        self.help_manager = create_help_manager(self.root)

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动状态更新定时器
        self.start_status_timer()

        logger.info("现代化主窗口初始化完成")

    def configure_display_settings(self):
        """配置显示设置以提高字体清晰度"""
        try:
            # 设置DPI感知
            import ctypes
            try:
                ctypes.windll.shcore.SetProcessDpiAwareness(1)
            except:
                try:
                    ctypes.windll.user32.SetProcessDPIAware()
                except:
                    pass

            # 配置tkinter字体渲染
            self.root.tk.call('tk', 'scaling', 1.0)

        except Exception as e:
            logger.warning(f"配置显示设置失败: {e}")

    def set_window_icon(self):
        """设置窗口图标"""
        try:
            # 获取图标文件路径
            icon_path = Path(__file__).parent.parent / "1.ico"

            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
                logger.info(f"成功设置窗口图标: {icon_path}")
            else:
                logger.warning(f"图标文件不存在: {icon_path}")

        except Exception as e:
            logger.error(f"设置窗口图标失败: {e}")

    def center_window(self, width, height):
        """居中显示窗口"""
        try:
            # 先设置窗口大小
            self.root.geometry(f"{width}x{height}")

            # 强制更新窗口以获取准确信息
            self.root.update_idletasks()

            # 获取屏幕尺寸
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # 获取任务栏高度（Windows）
            taskbar_height = 40  # 估算任务栏高度
            available_height = screen_height - taskbar_height

            # 计算居中位置
            x = max(0, (screen_width - width) // 2)
            y = max(0, (available_height - height) // 2)

            # 确保窗口不会超出屏幕边界
            if x + width > screen_width:
                x = max(0, screen_width - width)
            if y + height > available_height:
                y = max(0, available_height - height)

            # 设置窗口位置
            self.root.geometry(f"{width}x{height}+{x}+{y}")

            # 再次更新确保位置正确
            self.root.update_idletasks()

        except Exception as e:
            # 如果居中失败，使用默认位置
            self.root.geometry(f"{width}x{height}")

    def setup_system_tray(self):
        """设置系统托盘"""
        try:
            self.tray_manager = SystemTrayManager(self.root, "Outlook邮件定时发送工具 v2.0")

            # 设置托盘退出回调
            self.tray_manager.set_exit_callback(self.on_closing)

            if self.tray_manager.is_available():
                logger.info("系统托盘功能已启用")
            else:
                logger.warning("系统托盘功能不可用")

        except Exception as e:
            logger.error(f"设置系统托盘失败: {e}")
            self.tray_manager = None

    def setup_window(self):
        """设置窗口属性"""
        self.root.title("Outlook邮件定时发送工具 v2.0")

        # 设置程序图标
        self.set_window_icon()

        # 设置窗口背景色
        self.root.configure(bg=self.colors['bg_primary'])

        # 获取配置的窗口大小 - 紧凑版本
        width = config_manager.get('app.window_width', 600)
        height = config_manager.get('app.window_height', 500)

        # 设置窗口大小和居中显示
        self.center_window(width, height)
        self.root.minsize(600, 500)
    
    def create_widgets(self):
        """创建现代化界面组件"""
        # 创建菜单栏
        self.create_menu_bar()

        # 创建通知系统
        self.notification_manager = NotificationManager(self.root)

        # 创建进度指示器
        self.progress_indicator = ProgressIndicator(self.root)

        # 创建工具栏
        self.create_modern_toolbar()

        # 创建主内容区域
        self.create_modern_content()

        # 创建现代化状态栏
        self.status_display = StatusDisplay(self.root)
    
    def create_menu_bar(self):
        """创建现代化菜单栏"""
        menubar = tk.Menu(self.root,
                         bg=self.colors['bg_primary'],
                         fg=self.colors['text_primary'],
                         activebackground=self.colors['primary_light'],
                         activeforeground=self.colors['text_white'],
                         font=self.fonts['default'])
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0,
                           bg=self.colors['bg_primary'],
                           fg=self.colors['text_primary'],
                           activebackground=self.colors['primary_light'],
                           activeforeground=self.colors['text_white'],
                           font=self.fonts['default'])
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建任务", command=self.new_task, accelerator="Ctrl+N")
        file_menu.add_separator()
        file_menu.add_command(label="导入任务", command=self.import_tasks)
        file_menu.add_command(label="导出任务", command=self.export_tasks)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing, accelerator="Ctrl+Q")

        # 任务菜单
        task_menu = tk.Menu(menubar, tearoff=0,
                           bg=self.colors['bg_primary'],
                           fg=self.colors['text_primary'],
                           activebackground=self.colors['primary_light'],
                           activeforeground=self.colors['text_white'],
                           font=self.fonts['default'])
        menubar.add_cascade(label="任务", menu=task_menu)
        task_menu.add_command(label="启动调度器", command=self.start_scheduler)
        task_menu.add_command(label="停止调度器", command=self.stop_scheduler)
        task_menu.add_separator()
        task_menu.add_command(label="刷新任务", command=self.refresh_tasks)
        task_menu.add_command(label="清理已完成任务", command=self.cleanup_completed_tasks)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0,
                            bg=self.colors['bg_primary'],
                            fg=self.colors['text_primary'],
                            activebackground=self.colors['primary_light'],
                            activeforeground=self.colors['text_white'],
                            font=self.fonts['default'])
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="测试Outlook连接", command=self.test_outlook_connection)
        tools_menu.add_command(label="查看日志", command=self.view_logs)
        tools_menu.add_command(label="设置", command=self.show_settings)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0,
                           bg=self.colors['bg_primary'],
                           fg=self.colors['text_primary'],
                           activebackground=self.colors['primary_light'],
                           activeforeground=self.colors['text_white'],
                           font=self.fonts['default'])
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
        
        # 绑定快捷键
        self.root.bind('<Control-n>', lambda e: self.new_task())
        self.root.bind('<Control-q>', lambda e: self.on_closing())
        self.root.bind('<F5>', lambda e: self.refresh_tasks())
    
    def create_modern_toolbar(self):
        """创建现代化工具栏"""
        # 工具栏容器 - 紧凑版本
        toolbar_container = tk.Frame(self.root, bg=self.colors['bg_primary'])
        toolbar_container.pack(fill='x', padx=8, pady=(8, 4))

        # 工具栏卡片 - 紧凑版本
        toolbar_card, toolbar_content = create_compact_card_frame(toolbar_container)
        toolbar_card.pack(fill='x')

        # 左侧按钮组
        left_buttons = tk.Frame(toolbar_content, bg=self.colors['bg_primary'])
        left_buttons.pack(side='left', fill='y')

        # 调度器控制按钮 - 紧凑版本
        self.start_btn = ttk.Button(left_buttons, text="▶ 启动",
                                   style='Compact.Success.TButton',
                                   command=self.start_scheduler)
        self.start_btn.pack(side='left', padx=(0, 4))

        self.stop_btn = ttk.Button(left_buttons, text="⏸ 停止",
                                  style='Compact.Warning.TButton',
                                  command=self.stop_scheduler, state='disabled')
        self.stop_btn.pack(side='left', padx=(0, 8))

        # 分隔线
        separator = tk.Frame(left_buttons, bg=self.colors['border'], width=1)
        separator.pack(side='left', fill='y', padx=(0, 8), pady=2)

        # 任务操作按钮
        ttk.Button(left_buttons, text="➕ 新建",
                  style='Compact.Primary.TButton',
                  command=self.new_task).pack(side='left', padx=(0, 4))

        ttk.Button(left_buttons, text="🔄 刷新",
                  style='Compact.Secondary.TButton',
                  command=self.refresh_tasks).pack(side='left', padx=(0, 4))

        # 右侧状态信息
        right_info = tk.Frame(toolbar_content, bg=self.colors['bg_primary'])
        right_info.pack(side='right', fill='y')

        self.scheduler_status_var = tk.StringVar(value="调度器: 已停止")
        status_label = tk.Label(right_info, textvariable=self.scheduler_status_var,
                               bg=self.colors['bg_primary'],
                               fg=self.colors['text_secondary'],
                               font=self.fonts['default'])
        status_label.pack(side='right', pady=4)
    
    def create_modern_content(self):
        """创建现代化主内容区域"""
        # 主内容容器 - 紧凑版本
        content_container = tk.Frame(self.root, bg=self.colors['bg_primary'])
        content_container.pack(fill='both', expand=True, padx=8, pady=(0, 8))

        # 创建现代化任务管理器
        self.task_manager = ModernTaskManagerFrame(content_container)
        self.task_manager.pack(fill='both', expand=True)

        # 设置回调函数
        self.task_manager.on_task_add = self.add_task
        self.task_manager.on_task_update = self.update_task
        self.task_manager.on_task_delete = self.delete_task
        self.task_manager.on_task_pause = self.pause_task
        self.task_manager.on_task_resume = self.resume_task
        self.task_manager.on_task_cancel = self.cancel_task
        self.task_manager.on_task_retry = self.retry_task
    
    # 状态栏已在create_widgets中创建，这里不需要了
    
    def setup_scheduler_callbacks(self):
        """设置调度器回调"""
        task_scheduler.add_task_status_callback(self.on_task_status_changed)
        task_scheduler.add_task_completed_callback(self.on_task_completed)
    
    def load_initial_data(self):
        """加载初始数据"""
        try:
            # 加载任务
            task_scheduler.load_tasks_from_config()
            self.refresh_tasks()
            
            # 自动启动调度器（如果配置允许）
            if config_manager.get('app.auto_start_scheduler', False):
                self.start_scheduler()
                
        except Exception as e:
            logger.error(f"加载初始数据失败: {e}")
            self.notification_manager.show_error(f"加载初始数据失败: {str(e)}")
    
    def new_task(self):
        """新建任务"""
        self.task_manager.add_task()
    
    def add_task(self, task: EmailTask):
        """添加任务"""
        try:
            if task_scheduler.add_task(task):
                self.save_tasks()
                self.refresh_tasks()
                self.status_display.set_status(f"已添加任务: {task.name}", 'success')
                self.notification_manager.show_success(f"成功添加任务: {task.name}")
                logger.info(f"添加任务成功: {task.name}")
            else:
                self.notification_manager.show_error("添加任务失败")
        except Exception as e:
            logger.error(f"添加任务失败: {e}")
            self.notification_manager.show_error(f"添加任务时发生错误: {str(e)}")
    
    def update_task(self, task: EmailTask):
        """更新任务"""
        try:
            if task_scheduler.update_task(task):
                self.save_tasks()
                self.refresh_tasks()
                self.status_display.set_status(f"已更新任务: {task.name}", 'success')
                self.notification_manager.show_success(f"成功更新任务: {task.name}")
                logger.info(f"更新任务成功: {task.name}")
            else:
                self.notification_manager.show_error("更新任务失败")
        except Exception as e:
            logger.error(f"更新任务失败: {e}")
            self.notification_manager.show_error(f"更新任务时发生错误: {str(e)}")
    
    def delete_task(self, task_id: str):
        """删除任务"""
        try:
            task = task_scheduler.get_task(task_id)
            if task and task_scheduler.remove_task(task_id):
                self.save_tasks()
                self.refresh_tasks()
                self.status_display.set_status(f"已删除任务: {task.name}", 'success')
                self.notification_manager.show_success(f"成功删除任务: {task.name}")
                logger.info(f"删除任务成功: {task.name}")
            else:
                self.notification_manager.show_error("删除任务失败")
        except Exception as e:
            logger.error(f"删除任务失败: {e}")
            self.notification_manager.show_error(f"删除任务时发生错误: {str(e)}")

    def pause_task(self, task_id: str):
        """暂停任务"""
        try:
            task = task_scheduler.get_task(task_id)
            if task and task_scheduler.pause_task(task_id):
                self.save_tasks()
                self.status_display.set_status(f"已暂停任务: {task.name}", 'warning')
                self.notification_manager.show_info(f"已暂停任务: {task.name}")
                logger.info(f"暂停任务成功: {task.name}")
        except Exception as e:
            logger.error(f"暂停任务失败: {e}")
            self.notification_manager.show_error(f"暂停任务时发生错误: {str(e)}")

    def resume_task(self, task_id: str):
        """恢复任务"""
        try:
            task = task_scheduler.get_task(task_id)
            if task and task_scheduler.resume_task(task_id):
                self.save_tasks()
                self.status_display.set_status(f"已恢复任务: {task.name}", 'success')
                self.notification_manager.show_success(f"已恢复任务: {task.name}")
                logger.info(f"恢复任务成功: {task.name}")
        except Exception as e:
            logger.error(f"恢复任务失败: {e}")
            self.notification_manager.show_error(f"恢复任务时发生错误: {str(e)}")

    def cancel_task(self, task_id: str):
        """取消任务"""
        try:
            task = task_scheduler.get_task(task_id)
            if task and task_scheduler.cancel_task(task_id):
                self.save_tasks()
                self.status_display.set_status(f"已取消任务: {task.name}", 'warning')
                self.notification_manager.show_warning(f"已取消任务: {task.name}")
                logger.info(f"取消任务成功: {task.name}")
        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            self.notification_manager.show_error(f"取消任务时发生错误: {str(e)}")

    def retry_task(self, task_id: str):
        """重试任务"""
        try:
            task = task_scheduler.get_task(task_id)
            if task and task_scheduler.retry_task(task_id):
                self.save_tasks()
                self.status_display.set_status(f"已重试任务: {task.name}", 'success')
                self.notification_manager.show_info(f"已重试任务: {task.name}")
                logger.info(f"重试任务成功: {task.name}")
        except Exception as e:
            logger.error(f"重试任务失败: {e}")
            self.notification_manager.show_error(f"重试任务时发生错误: {str(e)}")
    
    def start_scheduler(self):
        """启动调度器"""
        def start_async():
            try:
                logger.info("开始启动调度器...")
                task_scheduler.start()

                # 在主线程中更新UI
                def update_ui():
                    self.start_btn.config(state='disabled')
                    self.stop_btn.config(state='normal')
                    self.update_scheduler_status()
                    self.status_display.set_status("调度器已启动", 'success')
                    self.notification_manager.show_success("调度器已启动")
                    logger.info("调度器启动成功")

                self.root.after(0, update_ui)

            except Exception as e:
                logger.error(f"启动调度器失败: {e}")

                def show_error_ui():
                    self.start_btn.config(state='normal')
                    self.stop_btn.config(state='disabled')
                    self.notification_manager.show_error(f"启动调度器失败: {str(e)}")

                self.root.after(0, show_error_ui)

        # 在后台线程中启动调度器
        import threading
        threading.Thread(target=start_async, daemon=True).start()

        # 立即禁用启动按钮，防止重复点击
        self.start_btn.config(state='disabled')
        self.status_display.set_status("正在启动调度器...", 'warning')
        self.progress_indicator.show("启动调度器中...")
    
    def stop_scheduler(self):
        """停止调度器"""
        def stop_async():
            try:
                logger.info("开始停止调度器...")
                task_scheduler.stop()

                # 在主线程中更新UI
                def update_ui():
                    self.start_btn.config(state='normal')
                    self.stop_btn.config(state='disabled')
                    self.update_scheduler_status()
                    self.status_display.set_status("调度器已停止", 'warning')
                    self.notification_manager.show_info("调度器已停止")
                    self.progress_indicator.hide()
                    logger.info("调度器停止成功")

                self.root.after(0, update_ui)

            except Exception as e:
                logger.error(f"停止调度器失败: {e}")

                def show_error_ui():
                    self.notification_manager.show_error(f"停止调度器失败: {str(e)}")
                    self.progress_indicator.hide()

                self.root.after(0, show_error_ui)

        # 在后台线程中停止调度器
        import threading
        threading.Thread(target=stop_async, daemon=True).start()

        # 立即禁用停止按钮，防止重复点击
        self.stop_btn.config(state='disabled')
        self.status_display.set_status("正在停止调度器...", 'warning')
        self.progress_indicator.show("停止调度器中...")
    
    def refresh_tasks(self):
        """刷新任务列表"""
        tasks = task_scheduler.get_all_tasks()
        self.task_manager.update_tasks(tasks)
        self.update_status_bar()
    
    def save_tasks(self):
        """保存任务"""
        try:
            task_scheduler.save_tasks_to_config()
        except Exception as e:
            logger.error(f"保存任务失败: {e}")
    
    def update_status_bar(self):
        """更新状态栏"""
        tasks = task_scheduler.get_all_tasks()
        self.status_display.set_task_count(len(tasks))
        self.update_scheduler_status()

    def update_scheduler_status(self):
        """更新调度器状态"""
        running = task_scheduler.is_running()
        running_count = task_scheduler.get_running_task_count()

        # 添加调试打印
        print(f"[DEBUG] MainWindow.update_scheduler_status: running={running}, running_count={running_count}")
        logger.debug(f"状态更新: 调度器运行={running}, 运行中任务数={running_count}")

        self.status_display.set_scheduler_status(running, running_count)

        if running:
            status_text = f"调度器: 运行中 ({running_count} 个任务执行中)"
        else:
            status_text = "调度器: 已停止"

        self.scheduler_status_var.set(status_text)
        print(f"[DEBUG] 工具栏状态文本已设置为: {status_text}")

    def start_status_timer(self):
        """启动状态更新定时器"""
        self.update_scheduler_status()
        # 每1秒更新一次调度器状态，确保及时显示运行中的任务
        self.root.after(1000, self.start_status_timer)
    
    def on_task_status_changed(self, task: EmailTask):
        """任务状态变化回调"""
        # 在主线程中更新UI
        def update_ui():
            self.refresh_tasks()
            self.update_scheduler_status()  # 同时更新调度器状态

        self.root.after(0, update_ui)
    
    def on_task_completed(self, task: EmailTask, success: bool, message: str):
        """任务完成回调"""
        def update_ui():
            if success:
                self.status_display.set_status(f"任务完成: {task.name}", 'success')
                self.notification_manager.show_success(f"任务 '{task.name}' 执行成功！")
            else:
                self.status_display.set_status(f"任务失败: {task.name}", 'error')
                self.notification_manager.show_error(f"任务 '{task.name}' 执行失败: {message}")

            self.refresh_tasks()

        # 在主线程中更新UI
        self.root.after(0, update_ui)
    
    def cleanup_completed_tasks(self):
        """清理已完成的任务"""
        if ask_yes_no(self.root, "确认清理", "确定要清理所有已完成和已取消的任务吗？"):
            try:
                tasks = task_scheduler.get_all_tasks()
                removed_count = 0
                
                for task in tasks:
                    if task.status in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]:
                        if task_scheduler.remove_task(task.id):
                            removed_count += 1
                
                self.save_tasks()
                self.refresh_tasks()
                self.status_bar.set_status(f"已清理 {removed_count} 个任务")
                show_info(self.root, "清理完成", f"已清理 {removed_count} 个已完成的任务")
                
            except Exception as e:
                logger.error(f"清理任务失败: {e}")
                show_error(self.root, "清理失败", f"清理任务时发生错误：\n{str(e)}")
    
    def test_outlook_connection(self):
        """测试Outlook连接"""
        # 立即显示开始测试的提示
        self.status_display.set_status("正在测试Outlook连接...", 'info')

        def test_connection():
            try:
                from email_sender.outlook_sender import OutlookSender
                from gui.components import show_info, show_error

                sender = OutlookSender()
                success, message = sender.test_connection()

                def show_result():
                    if success:
                        self.status_display.set_status("Outlook连接测试成功", 'success')
                        show_info(self.root, "连接测试成功", f"✅ Outlook连接正常！\n\n{message}")
                    else:
                        self.status_display.set_status("Outlook连接测试失败", 'error')
                        show_error(self.root, "连接测试失败", f"❌ 无法连接到Outlook\n\n{message}")

                self.root.after(0, show_result)

            except Exception as e:
                def show_error_result():
                    self.status_display.set_status("Outlook连接测试出错", 'error')
                    show_error(self.root, "连接测试错误", f"❌ 测试过程中发生错误：\n\n{str(e)}")

                self.root.after(0, show_error_result)

        # 在新线程中执行测试
        threading.Thread(target=test_connection, daemon=True).start()
    
    def import_tasks(self):
        """导入任务"""
        show_info(self.root, "功能提示", "导入任务功能正在开发中...")
    
    def export_tasks(self):
        """导出任务"""
        show_info(self.root, "功能提示", "导出任务功能正在开发中...")
    
    def view_logs(self):
        """查看日志"""
        show_info(self.root, "功能提示", "查看日志功能正在开发中...")
    
    def show_settings(self):
        """显示设置"""
        show_info(self.root, "功能提示", "设置功能正在开发中...")
    
    def show_help(self):
        """显示帮助"""
        if hasattr(self, 'help_manager'):
            self.help_manager.show_help()

    def show_about(self):
        """显示关于"""
        if hasattr(self, 'help_manager'):
            self.help_manager.show_about()
    
    def on_closing(self):
        """窗口关闭事件"""
        try:
            # 显示退出确认对话框
            if not self.confirm_exit():
                return

            # 停止调度器
            if task_scheduler.is_running():
                task_scheduler.stop()

            # 保存任务
            self.save_tasks()

            # 保存窗口大小
            geometry = self.root.geometry()
            width, height = geometry.split('+')[0].split('x')
            config_manager.set('app.window_width', int(width))
            config_manager.set('app.window_height', int(height))

            # 清理系统托盘
            if hasattr(self, 'tray_manager') and self.tray_manager:
                self.tray_manager.cleanup()

            logger.info("应用程序正常退出")
            self.root.destroy()

        except Exception as e:
            logger.error(f"关闭应用程序时发生错误: {e}")
            # 确保托盘也被清理
            try:
                if hasattr(self, 'tray_manager') and self.tray_manager:
                    self.tray_manager.cleanup()
            except:
                pass
            self.root.destroy()

    def confirm_exit(self):
        """确认退出对话框"""
        try:
            # 检查是否有正在运行的任务
            running_tasks = []
            if hasattr(self, 'task_manager_frame') and self.task_manager_frame:
                for task in self.task_manager_frame.tasks:
                    if task.status in [TaskStatus.RUNNING, TaskStatus.PENDING]:
                        running_tasks.append(task)

            # 构建提示消息
            if running_tasks:
                message = f"当前有 {len(running_tasks)} 个任务正在运行或等待执行。\n\n确定要退出程序吗？\n\n退出后这些任务将被停止。"
                title = "确认退出 - 有任务正在运行"
            else:
                message = "确定要退出 Outlook邮件定时发送工具吗？"
                title = "确认退出"

            # 显示确认对话框
            result = messagebox.askyesno(
                title,
                message,
                icon='question',
                default='no',  # 默认选择"否"，更安全
                parent=self.root
            )

            return result

        except Exception as e:
            logger.error(f"显示退出确认对话框时出错: {e}")
            # 如果出错，默认允许退出
            return True
    
    def run(self):
        """运行应用程序"""
        try:
            self.root.mainloop()
        except Exception as e:
            logger.critical(f"应用程序运行时发生严重错误: {e}")
            raise
