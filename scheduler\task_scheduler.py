"""
任务调度器模块
负责管理和执行邮件发送任务
"""

import threading
import time
from datetime import datetime, timedelta
from typing import List, Dict, Callable, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, Future
import queue

from scheduler.task_model import EmailTask, TaskStatus, SendHistory
from email_sender.outlook_sender import OutlookSender
from config.settings import config_manager
from utils.logger import logger, log_function_call, log_task_operation


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, max_workers: int = 3):
        self.tasks: Dict[str, EmailTask] = {}
        self.running_tasks: Dict[str, Future] = {}
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        self._running = False
        self._scheduler_thread = None
        self._lock = threading.RLock()
        
        # 回调函数
        self.task_status_callbacks: List[Callable[[EmailTask], None]] = []
        self.task_completed_callbacks: List[Callable[[EmailTask, bool, str], None]] = []
        
        # 配置
        self.retry_interval = config_manager.get('email.retry_interval', 300)  # 5分钟
        self.check_interval = 1  # 检查间隔1秒
        
        logger.info("任务调度器初始化完成")
    
    def add_task_status_callback(self, callback: Callable[[EmailTask], None]):
        """添加任务状态变化回调"""
        self.task_status_callbacks.append(callback)
    
    def add_task_completed_callback(self, callback: Callable[[EmailTask, bool, str], None]):
        """添加任务完成回调"""
        self.task_completed_callbacks.append(callback)
    
    def _notify_status_change(self, task: EmailTask):
        """通知任务状态变化"""
        # 记录状态变化
        logger.debug(f"任务状态变化: {task.name} -> {task.status}")

        # 通知所有回调
        for callback in self.task_status_callbacks:
            try:
                callback(task)
            except Exception as e:
                logger.error(f"执行状态变化回调失败: {e}")

    def _notify_task_completed(self, task: EmailTask, success: bool, message: str):
        """通知任务完成"""
        # 记录任务完成
        logger.info(f"任务完成: {task.name} - 成功: {success}")
        if not success:
            logger.error(f"任务失败原因: {message}")

        # 通知所有回调
        for callback in self.task_completed_callbacks:
            try:
                callback(task, success, message)
            except Exception as e:
                logger.error(f"执行任务完成回调失败: {e}")
    
    @log_function_call
    def add_task(self, task: EmailTask) -> bool:
        """添加任务"""
        try:
            with self._lock:
                if task.id in self.tasks:
                    logger.warning(f"任务已存在: {task.id}")
                    return False
                
                self.tasks[task.id] = task
                logger.info(f"添加任务: {task.name} (ID: {task.id})")
                self._notify_status_change(task)
                return True
                
        except Exception as e:
            logger.error(f"添加任务失败: {e}")
            return False
    
    @log_function_call
    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        try:
            with self._lock:
                if task_id not in self.tasks:
                    logger.warning(f"任务不存在: {task_id}")
                    return False
                
                task = self.tasks[task_id]
                
                # 如果任务正在运行，先取消
                if task_id in self.running_tasks:
                    self.cancel_task(task_id)
                
                del self.tasks[task_id]
                logger.info(f"移除任务: {task.name} (ID: {task_id})")
                return True
                
        except Exception as e:
            logger.error(f"移除任务失败: {e}")
            return False
    
    @log_function_call
    def update_task(self, task: EmailTask) -> bool:
        """更新任务"""
        try:
            with self._lock:
                if task.id not in self.tasks:
                    logger.warning(f"任务不存在: {task.id}")
                    return False
                
                # 如果任务正在运行，不允许更新
                if task.id in self.running_tasks:
                    logger.warning(f"任务正在运行，无法更新: {task.id}")
                    return False
                
                self.tasks[task.id] = task
                logger.info(f"更新任务: {task.name} (ID: {task.id})")
                self._notify_status_change(task)
                return True
                
        except Exception as e:
            logger.error(f"更新任务失败: {e}")
            return False
    
    @log_function_call
    def get_task(self, task_id: str) -> Optional[EmailTask]:
        """获取任务"""
        with self._lock:
            return self.tasks.get(task_id)
    
    @log_function_call
    def get_all_tasks(self) -> List[EmailTask]:
        """获取所有任务"""
        with self._lock:
            return list(self.tasks.values())
    
    @log_function_call
    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        try:
            with self._lock:
                task = self.tasks.get(task_id)
                if not task:
                    return False
                
                if task.status == TaskStatus.PENDING:
                    task.mark_paused()
                    logger.info(f"暂停任务: {task.name}")
                    self._notify_status_change(task)
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"暂停任务失败: {e}")
            return False
    
    @log_function_call
    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        try:
            with self._lock:
                task = self.tasks.get(task_id)
                if not task:
                    return False
                
                if task.status == TaskStatus.PAUSED:
                    task.status = TaskStatus.PENDING
                    logger.info(f"恢复任务: {task.name}")
                    self._notify_status_change(task)
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"恢复任务失败: {e}")
            return False
    
    @log_function_call
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            with self._lock:
                task = self.tasks.get(task_id)
                if not task:
                    return False
                
                # 如果任务正在运行，尝试取消
                if task_id in self.running_tasks:
                    future = self.running_tasks[task_id]
                    future.cancel()
                    del self.running_tasks[task_id]
                
                task.mark_cancelled()
                logger.info(f"取消任务: {task.name}")
                self._notify_status_change(task)
                return True
                
        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return False
    
    @log_function_call
    def retry_task(self, task_id: str) -> bool:
        """重试失败的任务"""
        try:
            with self._lock:
                task = self.tasks.get(task_id)
                if not task:
                    return False
                
                if task.can_retry():
                    task.reset_for_retry()
                    logger.info(f"重试任务: {task.name}")
                    self._notify_status_change(task)
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"重试任务失败: {e}")
            return False
    
    def _execute_task(self, task: EmailTask) -> Tuple[bool, str]:
        """执行单个任务"""
        try:
            logger.info(f"开始执行任务: {task.name}")

            # 任务已经在调度器中被标记为运行中，这里不需要重复标记

            # 创建邮件发送器并执行
            sender = OutlookSender()
            success, message = sender.send_draft_by_subject(
                task.subject_keyword,
                task.include_attachments
            )
            
            # 更新任务状态
            if success:
                task.mark_completed()
                logger.info(f"任务执行成功: {task.name}")
            else:
                task.mark_failed(message)
                logger.error(f"任务执行失败: {task.name} - {message}")
            
            self._notify_status_change(task)
            self._notify_task_completed(task, success, message)
            
            # 记录历史
            history = SendHistory.create_new(task, success, None if success else message)
            config_manager.add_history_record(history)
            
            return success, message
            
        except Exception as e:
            error_msg = f"执行任务时发生异常: {e}"
            logger.exception(error_msg)
            
            task.mark_failed(error_msg)
            self._notify_status_change(task)
            self._notify_task_completed(task, False, error_msg)
            
            return False, error_msg
    
    def _scheduler_loop(self):
        """调度器主循环"""
        logger.info("任务调度器开始运行")
        
        while self._running:
            try:
                current_time = datetime.now()
                
                with self._lock:
                    # 检查需要执行的任务
                    for task in list(self.tasks.values()):
                        if (task.can_execute() and
                            current_time >= task.scheduled_time and
                            task.id not in self.running_tasks):

                            # 立即标记任务为运行中，防止重复执行
                            task.mark_running()
                            self._notify_status_change(task)
                            logger.info(f"开始执行任务: {task.name} (ID: {task.id})")

                            # 提交任务到线程池
                            future = self.executor.submit(self._execute_task, task)
                            self.running_tasks[task.id] = future
                            logger.debug(f"任务已提交到线程池: {task.id}, 当前运行任务数: {len(self.running_tasks)}")

                            # 设置完成回调
                            def cleanup_task(task_id):
                                def callback(fut):
                                    with self._lock:
                                        if task_id in self.running_tasks:
                                            del self.running_tasks[task_id]
                                            logger.debug(f"任务完成，从运行列表移除: {task_id}, 剩余运行任务数: {len(self.running_tasks)}")
                                return callback

                            future.add_done_callback(cleanup_task(task.id))
                    
                    # 清理已完成的任务
                    completed_tasks = []
                    for task_id, future in list(self.running_tasks.items()):
                        if future.done():
                            completed_tasks.append(task_id)
                    
                    for task_id in completed_tasks:
                        if task_id in self.running_tasks:
                            del self.running_tasks[task_id]
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"调度器循环中发生错误: {e}")
                time.sleep(self.check_interval)
        
        logger.info("任务调度器停止运行")
    
    @log_task_operation("启动调度器")
    def start(self):
        """启动调度器"""
        if self._running:
            logger.warning("调度器已在运行")
            return
        
        self._running = True
        self._scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self._scheduler_thread.start()
        logger.info("任务调度器已启动")
    
    @log_task_operation("停止调度器")
    def stop(self):
        """停止调度器"""
        if not self._running:
            logger.warning("调度器未在运行")
            return
        
        self._running = False
        
        # 等待调度器线程结束
        if self._scheduler_thread and self._scheduler_thread.is_alive():
            self._scheduler_thread.join(timeout=5)
        
        # 取消所有运行中的任务
        with self._lock:
            for future in self.running_tasks.values():
                future.cancel()
            self.running_tasks.clear()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        logger.info("任务调度器已停止")
    
    def is_running(self) -> bool:
        """检查调度器是否在运行"""
        return self._running
    
    def get_running_task_count(self) -> int:
        """获取正在运行的任务数量"""
        with self._lock:
            count = len(self.running_tasks)
            if count > 0:
                logger.debug(f"当前运行中任务数: {count}, 任务ID: {list(self.running_tasks.keys())}")
            return count
    
    def load_tasks_from_config(self):
        """从配置文件加载任务"""
        try:
            tasks = config_manager.load_tasks()
            with self._lock:
                for task in tasks:
                    # 重置运行状态的任务
                    if task.status == TaskStatus.RUNNING:
                        task.status = TaskStatus.PENDING
                    
                    self.tasks[task.id] = task
                    self._notify_status_change(task)
            
            logger.info(f"从配置文件加载了 {len(tasks)} 个任务")
            
        except Exception as e:
            logger.error(f"加载任务失败: {e}")
    
    def save_tasks_to_config(self):
        """保存任务到配置文件"""
        try:
            with self._lock:
                tasks = list(self.tasks.values())
            config_manager.save_tasks(tasks)
            logger.debug(f"保存了 {len(tasks)} 个任务到配置文件")
            
        except Exception as e:
            logger.error(f"保存任务失败: {e}")


# 全局任务调度器实例
task_scheduler = TaskScheduler()
