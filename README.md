# Outlook邮件定时发送工具 v2.0

一个功能强大的Windows平台邮件定时发送工具，支持多任务管理、智能调度和错误重试。

## 功能特性

### 🚀 核心功能
- **多任务管理**：支持同时创建和管理多个邮件发送任务
- **精确定时**：支持秒级精度的定时发送
- **智能调度**：自动任务调度系统，支持暂停、恢复、取消操作
- **错误重试**：自动重试机制，提高发送成功率
- **附件支持**：完整支持包含附件的邮件发送

### 📊 管理功能
- **任务状态监控**：实时显示任务状态和倒计时
- **历史记录**：完整的发送历史记录和统计
- **配置管理**：灵活的配置系统，支持导入导出
- **日志记录**：详细的操作日志，便于问题排查

### 🎨 用户界面
- **现代化GUI**：基于tkinter的友好用户界面
- **任务列表**：直观的任务管理界面
- **状态显示**：实时状态栏和倒计时显示
- **右键菜单**：便捷的任务操作菜单

## 系统要求

- **操作系统**：Windows 7/8/10/11
- **Python版本**：Python 3.7+
- **必需软件**：Microsoft Outlook
- **依赖包**：见 requirements.txt

## 安装说明

### 1. 克隆项目
```bash
git clone <repository-url>
cd outlook-scheduler
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行程序
```bash
python main.py
```

## 使用指南

### 基本使用流程

1. **准备邮件**
   - 在Outlook中编写邮件
   - 保存到草稿箱

2. **创建任务**
   - 启动程序
   - 点击"新建任务"
   - 填写任务信息：
     - 任务名称
     - 邮件主题关键词
     - 发送时间
     - 是否包含附件

3. **启动调度器**
   - 点击"启动调度器"
   - 程序将自动监控和执行任务

4. **监控执行**
   - 查看任务状态
   - 监控倒计时
   - 查看执行结果

### 高级功能

#### 任务管理
- **编辑任务**：双击任务或右键选择编辑
- **暂停/恢复**：临时暂停任务执行
- **取消任务**：取消未执行的任务
- **重试失败**：重新执行失败的任务
- **复制任务**：基于现有任务创建新任务

#### 快速时间设置
程序提供多个快速时间设置选项：
- 1分钟后
- 5分钟后
- 10分钟后
- 30分钟后
- 1小时后
- 明天此时

#### 配置选项
- **自动删除已发送邮件**：发送成功后自动删除草稿
- **最大重试次数**：设置任务失败后的重试次数
- **重试间隔**：设置重试之间的等待时间

## 项目结构

```
outlook_scheduler/
├── main.py                 # 主程序入口
├── config/                 # 配置管理
│   ├── __init__.py
│   └── settings.py
├── gui/                    # 用户界面
│   ├── __init__.py
│   ├── main_window.py      # 主窗口
│   ├── task_manager.py     # 任务管理界面
│   ├── task_editor.py      # 任务编辑器
│   └── components.py       # UI组件
├── email_sender/           # 邮件发送
│   ├── __init__.py
│   └── outlook_sender.py   # Outlook邮件发送器
├── scheduler/              # 任务调度
│   ├── __init__.py
│   ├── task_scheduler.py   # 任务调度器
│   └── task_model.py       # 任务数据模型
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── logger.py           # 日志管理
│   └── helpers.py          # 辅助函数
├── requirements.txt        # 依赖包列表
└── README.md              # 项目说明
```

## 配置文件

程序会在 `config/` 目录下创建以下配置文件：

- `config.json`：应用程序配置
- `tasks.json`：任务数据
- `history.json`：发送历史记录

## 日志文件

日志文件保存在 `logs/` 目录下：

- `outlook_scheduler.log`：主日志文件
- 支持日志轮转，自动管理文件大小

## 故障排除

### 常见问题

1. **无法连接Outlook**
   - 确保Outlook已安装并配置好邮箱账户
   - 检查Outlook是否正在运行
   - 尝试重启Outlook

2. **找不到草稿邮件**
   - 检查邮件主题关键词是否正确
   - 确保邮件已保存在草稿箱中
   - 检查邮件格式是否正确

3. **任务执行失败**
   - 查看错误信息和日志文件
   - 检查网络连接
   - 确认邮箱账户状态

4. **程序启动失败**
   - 检查Python版本和依赖包
   - 以管理员权限运行
   - 查看启动日志

### 获取帮助

- 查看日志文件：`logs/outlook_scheduler.log`
- 使用程序内置的连接测试功能
- 检查系统要求和依赖包

## 开发说明

### 代码结构
项目采用模块化设计，各模块职责清晰：

- **config**：配置管理，支持JSON格式配置文件
- **gui**：用户界面，基于tkinter构建
- **email_sender**：邮件发送，封装Outlook COM接口
- **scheduler**：任务调度，支持多线程执行
- **utils**：工具模块，提供日志、辅助函数等

### 扩展开发
- 添加新的邮件发送方式
- 扩展任务调度功能
- 增加更多配置选项
- 改进用户界面

## 版本历史

### v2.0 (当前版本)
- 完全重构代码架构
- 添加多任务管理功能
- 改进用户界面
- 增加配置管理和日志系统
- 添加错误重试机制
- 支持任务暂停、恢复、取消
- 添加历史记录功能

### v1.0
- 基础的单任务定时发送功能
- 简单的GUI界面
- 基本的错误处理

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交问题报告和功能请求。如果您想贡献代码，请：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues：<repository-issues-url>
- 邮箱：<contact-email>

---

**注意**：本工具仅用于合法的邮件发送用途，请遵守相关法律法规和邮件服务提供商的使用条款。
