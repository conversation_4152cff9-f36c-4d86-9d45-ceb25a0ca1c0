"""
配置管理模块
负责应用程序配置的加载、保存和管理
"""

import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from scheduler.task_model import EmailTask, SendHistory


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.config_file = self.config_dir / "config.json"
        self.tasks_file = self.config_dir / "tasks.json"
        self.history_file = self.config_dir / "history.json"
        
        self._default_config = {
            "app": {
                "window_width": 800,
                "window_height": 600,
                "auto_save": True,
                "auto_save_interval": 30,  # 秒
                "max_history_records": 1000
            },
            "email": {
                "auto_delete_sent": True,
                "max_retries": 3,
                "retry_interval": 300,  # 秒
                "timeout": 30  # 秒
            }
        }
        
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                return self._merge_config(self._default_config, config)
            else:
                # 创建默认配置文件
                self.save_config(self._default_config)
                return self._default_config.copy()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self._default_config.copy()
    
    def _merge_config(self, default: Dict, user: Dict) -> Dict:
        """合并配置，用户配置覆盖默认配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def save_config(self, config: Optional[Dict[str, Any]] = None):
        """保存配置文件"""
        try:
            config_to_save = config or self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, key_path: str, default=None):
        """获取配置值，支持点号分隔的路径"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value: Any):
        """设置配置值，支持点号分隔的路径"""
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
        
        if self.get('app.auto_save', True):
            self.save_config()
    
    def load_tasks(self) -> List[EmailTask]:
        """加载任务列表"""
        try:
            if self.tasks_file.exists():
                with open(self.tasks_file, 'r', encoding='utf-8') as f:
                    tasks_data = json.load(f)
                return [EmailTask.from_dict(task_data) for task_data in tasks_data]
            return []
        except Exception as e:
            print(f"加载任务列表失败: {e}")
            return []
    
    def save_tasks(self, tasks: List[EmailTask]):
        """保存任务列表"""
        try:
            tasks_data = [task.to_dict() for task in tasks]
            with open(self.tasks_file, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存任务列表失败: {e}")
    
    def load_history(self) -> List[SendHistory]:
        """加载发送历史"""
        try:
            if self.history_file.exists():
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history_data = json.load(f)
                return [SendHistory.from_dict(record) for record in history_data]
            return []
        except Exception as e:
            print(f"加载发送历史失败: {e}")
            return []
    
    def save_history(self, history: List[SendHistory]):
        """保存发送历史"""
        try:
            # 限制历史记录数量
            max_records = self.get('app.max_history_records', 1000)
            if len(history) > max_records:
                history = history[-max_records:]
            
            history_data = [record.to_dict() for record in history]
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存发送历史失败: {e}")
    
    def add_history_record(self, record: SendHistory):
        """添加单条历史记录"""
        history = self.load_history()
        history.append(record)
        self.save_history(history)
    
    def cleanup_old_history(self, days: int = 30):
        """清理旧的历史记录"""
        try:
            history = self.load_history()
            cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            cutoff_date = cutoff_date.replace(day=cutoff_date.day - days)
            
            filtered_history = [
                record for record in history 
                if record.sent_time >= cutoff_date
            ]
            
            if len(filtered_history) != len(history):
                self.save_history(filtered_history)
                print(f"清理了 {len(history) - len(filtered_history)} 条历史记录")
        except Exception as e:
            print(f"清理历史记录失败: {e}")


# 全局配置管理器实例
config_manager = ConfigManager()
