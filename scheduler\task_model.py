"""
任务数据模型
定义邮件发送任务的数据结构和状态管理
"""

from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
import uuid


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消
    PAUSED = "paused"       # 已暂停


@dataclass
class EmailTask:
    """邮件发送任务数据模型"""
    id: str
    name: str                           # 任务名称
    subject_keyword: str                # 邮件主题关键词
    scheduled_time: datetime            # 计划发送时间
    include_attachments: bool = True    # 是否包含附件
    status: TaskStatus = TaskStatus.PENDING
    created_time: Optional[datetime] = None
    completed_time: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    executed: bool = False              # 是否已经执行过
    
    def __post_init__(self):
        """初始化后处理"""
        if self.created_time is None:
            self.created_time = datetime.now()
    
    @classmethod
    def create_new(cls, name: str, subject_keyword: str, scheduled_time: datetime, 
                   include_attachments: bool = True) -> 'EmailTask':
        """创建新任务"""
        return cls(
            id=str(uuid.uuid4()),
            name=name,
            subject_keyword=subject_keyword,
            scheduled_time=scheduled_time,
            include_attachments=include_attachments,
            created_time=datetime.now()
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 转换datetime为字符串
        if self.created_time:
            data['created_time'] = self.created_time.isoformat()
        if self.completed_time:
            data['completed_time'] = self.completed_time.isoformat()
        data['scheduled_time'] = self.scheduled_time.isoformat()
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EmailTask':
        """从字典创建任务"""
        # 转换字符串为datetime
        if 'created_time' in data and data['created_time']:
            data['created_time'] = datetime.fromisoformat(data['created_time'])
        if 'completed_time' in data and data['completed_time']:
            data['completed_time'] = datetime.fromisoformat(data['completed_time'])
        data['scheduled_time'] = datetime.fromisoformat(data['scheduled_time'])
        data['status'] = TaskStatus(data['status'])
        return cls(**data)
    
    def can_execute(self) -> bool:
        """检查任务是否可以执行"""
        return self.status == TaskStatus.PENDING and not self.executed
    
    def can_retry(self) -> bool:
        """检查任务是否可以重试"""
        return self.status == TaskStatus.FAILED and self.retry_count < self.max_retries
    
    def mark_running(self):
        """标记任务为运行中"""
        self.status = TaskStatus.RUNNING
        self.executed = True  # 标记为已执行，防止重复执行
    
    def mark_completed(self):
        """标记任务为已完成"""
        self.status = TaskStatus.COMPLETED
        self.completed_time = datetime.now()
    
    def mark_failed(self, error_message: str):
        """标记任务为失败"""
        self.status = TaskStatus.FAILED
        self.error_message = error_message
        self.retry_count += 1
    
    def mark_cancelled(self):
        """标记任务为已取消"""
        self.status = TaskStatus.CANCELLED
    
    def mark_paused(self):
        """标记任务为已暂停"""
        self.status = TaskStatus.PAUSED
    
    def reset_for_retry(self):
        """重置任务状态以便重试"""
        if self.can_retry():
            self.status = TaskStatus.PENDING
            self.error_message = None
            self.executed = False  # 允许重新执行
    
    def get_time_remaining(self) -> Optional[float]:
        """获取剩余时间（秒）"""
        if self.status not in [TaskStatus.PENDING, TaskStatus.PAUSED]:
            return None
        
        now = datetime.now()
        if now >= self.scheduled_time:
            return 0
        
        return (self.scheduled_time - now).total_seconds()
    
    def get_status_display(self) -> str:
        """获取状态显示文本"""
        status_map = {
            TaskStatus.PENDING: "等待执行",
            TaskStatus.RUNNING: "正在执行",
            TaskStatus.COMPLETED: "已完成",
            TaskStatus.FAILED: "执行失败",
            TaskStatus.CANCELLED: "已取消",
            TaskStatus.PAUSED: "已暂停"
        }
        return status_map.get(self.status, "未知状态")


@dataclass
class SendHistory:
    """发送历史记录"""
    id: str
    task_id: str
    task_name: str
    subject_keyword: str
    sent_time: datetime
    success: bool
    error_message: Optional[str] = None
    
    @classmethod
    def create_new(cls, task: EmailTask, success: bool, error_message: Optional[str] = None) -> 'SendHistory':
        """创建新的历史记录"""
        return cls(
            id=str(uuid.uuid4()),
            task_id=task.id,
            task_name=task.name,
            subject_keyword=task.subject_keyword,
            sent_time=datetime.now(),
            success=success,
            error_message=error_message
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['sent_time'] = self.sent_time.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SendHistory':
        """从字典创建历史记录"""
        data['sent_time'] = datetime.fromisoformat(data['sent_time'])
        return cls(**data)
