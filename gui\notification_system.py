"""
现代化通知系统
提供内联通知，替代弹窗提示
"""

import tkinter as tk
from tkinter import ttk
from gui.modern_theme import ModernTheme, create_notification_frame


class NotificationManager:
    """通知管理器"""
    
    def __init__(self, parent):
        self.parent = parent
        self.notifications = []
        self.max_notifications = 3
        self.auto_hide_delay = 1000  # 5秒后自动隐藏
        
        # 创建通知容器
        self.notification_container = tk.Frame(parent, bg=ModernTheme.COLORS['bg_primary'])
        self.notification_container.pack(fill='x', padx=16, pady=(8, 0))
    
    def show_notification(self, message, notification_type='info', auto_hide=True):
        """显示通知"""
        # 如果通知太多，移除最旧的
        if len(self.notifications) >= self.max_notifications:
            self.hide_notification(self.notifications[0])
        
        # 创建通知框架
        notification_frame = create_notification_frame(
            self.notification_container, message, notification_type
        )
        notification_frame.pack(fill='x', pady=(0, 8))
        
        # 添加关闭按钮
        close_btn = tk.Label(notification_frame, text='×',
                           bg=notification_frame['bg'],
                           fg=ModernTheme.COLORS['text_muted'],
                           font=('Segoe UI', 14, 'bold'),
                           cursor='hand2')
        close_btn.pack(side='right', padx=(8, 12), pady=8)
        close_btn.bind('<Button-1>', lambda e: self.hide_notification(notification_frame))
        
        # 添加到通知列表
        self.notifications.append(notification_frame)
        
        # 自动隐藏
        if auto_hide:
            self.parent.after(self.auto_hide_delay, 
                            lambda: self.hide_notification(notification_frame))
        
        return notification_frame
    
    def hide_notification(self, notification_frame):
        """隐藏通知"""
        if notification_frame in self.notifications:
            self.notifications.remove(notification_frame)
            notification_frame.destroy()
    
    def clear_all_notifications(self):
        """清除所有通知"""
        for notification in self.notifications[:]:
            self.hide_notification(notification)
    
    def show_success(self, message, auto_hide=True):
        """显示成功通知"""
        return self.show_notification(message, 'success', auto_hide)
    
    def show_error(self, message, auto_hide=True):
        """显示错误通知"""
        return self.show_notification(message, 'error', auto_hide)
    
    def show_warning(self, message, auto_hide=True):
        """显示警告通知"""
        return self.show_notification(message, 'warning', auto_hide)
    
    def show_info(self, message, auto_hide=True):
        """显示信息通知"""
        return self.show_notification(message, 'info', auto_hide)


class StatusDisplay:
    """状态显示组件"""
    
    def __init__(self, parent):
        self.parent = parent
        self.colors = ModernTheme.COLORS
        
        # 创建状态显示框架
        self.status_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], height=40)
        self.status_frame.pack(fill='x', side='bottom')
        self.status_frame.pack_propagate(False)
        
        # 左侧状态信息
        self.left_frame = tk.Frame(self.status_frame, bg=self.colors['bg_secondary'])
        self.left_frame.pack(side='left', fill='y', padx=16)
        
        # 主状态标签
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = tk.Label(self.left_frame, textvariable=self.status_var,
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_primary'],
                                   font=ModernTheme.FONTS['default'])
        self.status_label.pack(side='left', pady=12)
        
        # 分隔符
        separator1 = tk.Frame(self.left_frame, bg=self.colors['border'], width=1)
        separator1.pack(side='left', fill='y', padx=12, pady=8)
        
        # 任务计数
        self.task_count_var = tk.StringVar(value="任务: 0")
        self.task_count_label = tk.Label(self.left_frame, textvariable=self.task_count_var,
                                       bg=self.colors['bg_secondary'],
                                       fg=self.colors['text_secondary'],
                                       font=ModernTheme.FONTS['default'])
        self.task_count_label.pack(side='left', pady=12)
        
        # 分隔符
        separator2 = tk.Frame(self.left_frame, bg=self.colors['border'], width=1)
        separator2.pack(side='left', fill='y', padx=12, pady=8)
        
        # 调度器状态
        self.scheduler_var = tk.StringVar(value="调度器: 已停止")
        self.scheduler_label = tk.Label(self.left_frame, textvariable=self.scheduler_var,
                                      bg=self.colors['bg_secondary'],
                                      fg=self.colors['text_secondary'],
                                      font=ModernTheme.FONTS['default'])
        self.scheduler_label.pack(side='left', pady=12)
        
        # 右侧时间显示
        self.right_frame = tk.Frame(self.status_frame, bg=self.colors['bg_secondary'])
        self.right_frame.pack(side='right', fill='y', padx=16)
        
        self.time_var = tk.StringVar()
        self.time_label = tk.Label(self.right_frame, textvariable=self.time_var,
                                 bg=self.colors['bg_secondary'],
                                 fg=self.colors['text_muted'],
                                 font=ModernTheme.FONTS['small'])
        self.time_label.pack(side='right', pady=12)
        
        # 启动时间更新
        self.update_time()
    
    def update_time(self):
        """更新时间显示"""
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)
        self.parent.after(1000, self.update_time)
    
    def set_status(self, text, status_type='normal'):
        """设置状态文本"""
        self.status_var.set(text)
        
        # 根据状态类型设置颜色
        if status_type == 'success':
            self.status_label.config(fg=self.colors['success'])
        elif status_type == 'error':
            self.status_label.config(fg=self.colors['error'])
        elif status_type == 'warning':
            self.status_label.config(fg=self.colors['warning'])
        else:
            self.status_label.config(fg=self.colors['text_primary'])
    
    def set_task_count(self, count):
        """设置任务计数"""
        self.task_count_var.set(f"任务: {count}")
    
    def set_scheduler_status(self, running, running_count=0):
        """设置调度器状态"""
        # 添加调试打印
        print(f"[DEBUG] StatusDisplay.set_scheduler_status: running={running}, running_count={running_count}")

        if running:
            status_text = f"调度器: 运行中 ({running_count} 个任务执行中)"
            self.scheduler_label.config(fg=self.colors['success'])
        else:
            status_text = "调度器: 已停止"
            self.scheduler_label.config(fg=self.colors['text_secondary'])

        self.scheduler_var.set(status_text)
        print(f"[DEBUG] 状态文本已设置为: {status_text}")


class ProgressIndicator:
    """进度指示器"""
    
    def __init__(self, parent):
        self.parent = parent
        self.colors = ModernTheme.COLORS
        self.is_visible = False
        
        # 创建进度条框架
        self.progress_frame = tk.Frame(parent, bg=self.colors['bg_primary'], height=4)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.progress_frame,
                                          variable=self.progress_var,
                                          mode='indeterminate')
    
    def show(self, message="处理中..."):
        """显示进度指示器"""
        if not self.is_visible:
            self.progress_frame.pack(fill='x', padx=16, pady=(0, 8))
            self.progress_bar.pack(fill='x', padx=0, pady=2)
            self.progress_bar.start(10)
            self.is_visible = True
    
    def hide(self):
        """隐藏进度指示器"""
        if self.is_visible:
            self.progress_bar.stop()
            self.progress_frame.pack_forget()
            self.is_visible = False
    
    def set_progress(self, value):
        """设置进度值（0-100）"""
        self.progress_bar.config(mode='determinate')
        self.progress_var.set(value)
    
    def set_indeterminate(self):
        """设置为不确定进度"""
        self.progress_bar.config(mode='indeterminate')
        self.progress_bar.start(10)
