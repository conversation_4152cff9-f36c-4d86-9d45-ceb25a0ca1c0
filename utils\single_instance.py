"""
单实例运行管理模块
确保程序只能运行一个实例
"""

import os
import sys
import time
import tempfile
import threading
import tkinter as tk
from tkinter import messagebox
from pathlib import Path
import win32gui
import win32con
import win32process
import win32api
import psutil

from utils.logger import logger


class SingleInstanceManager:
    """单实例管理器"""
    
    def __init__(self, app_name="OutlookScheduler"):
        self.app_name = app_name
        self.lock_file = None
        self.lock_file_path = None
        self.window_title = "Outlook邮件定时发送工具 v2.0"
        
    def is_already_running(self):
        """检查是否已有实例在运行"""
        try:
            # 创建锁文件路径
            temp_dir = tempfile.gettempdir()
            self.lock_file_path = os.path.join(temp_dir, f"{self.app_name}.lock")
            
            # 检查锁文件是否存在
            if os.path.exists(self.lock_file_path):
                # 读取PID
                try:
                    with open(self.lock_file_path, 'r') as f:
                        pid = int(f.read().strip())
                    
                    # 检查进程是否还在运行
                    if self._is_process_running(pid):
                        logger.info(f"检测到程序已在运行，PID: {pid}")
                        return True
                    else:
                        # 进程不存在，删除过期的锁文件
                        os.remove(self.lock_file_path)
                        logger.info("删除过期的锁文件")
                except (ValueError, FileNotFoundError, PermissionError) as e:
                    logger.warning(f"处理锁文件时出错: {e}")
                    # 尝试删除损坏的锁文件
                    try:
                        os.remove(self.lock_file_path)
                    except:
                        pass
            
            # 创建新的锁文件
            self._create_lock_file()
            return False
            
        except Exception as e:
            logger.error(f"检查单实例时出错: {e}")
            return False
    
    def _is_process_running(self, pid):
        """检查指定PID的进程是否在运行"""
        try:
            process = psutil.Process(pid)
            # 检查进程名是否包含python
            if 'python' in process.name().lower():
                # 检查命令行参数是否包含我们的脚本
                cmdline = ' '.join(process.cmdline())
                if 'main.py' in cmdline or self.app_name.lower() in cmdline.lower():
                    return True
            return False
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            return False
    
    def _create_lock_file(self):
        """创建锁文件"""
        try:
            current_pid = os.getpid()
            with open(self.lock_file_path, 'w') as f:
                f.write(str(current_pid))
            logger.info(f"创建锁文件: {self.lock_file_path}, PID: {current_pid}")
        except Exception as e:
            logger.error(f"创建锁文件失败: {e}")
    
    def bring_existing_to_front(self):
        """将已存在的实例窗口置于前台"""
        try:
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    if self.window_title in window_text:
                        windows.append(hwnd)
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            if windows:
                # 找到窗口，将其置于前台
                hwnd = windows[0]
                
                # 如果窗口最小化，先恢复
                if win32gui.IsIconic(hwnd):
                    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                
                # 置于前台
                win32gui.SetForegroundWindow(hwnd)
                win32gui.BringWindowToTop(hwnd)
                
                logger.info("已将现有实例窗口置于前台")
                return True
            else:
                logger.warning("未找到现有实例窗口")
                return False
                
        except Exception as e:
            logger.error(f"置于前台失败: {e}")
            return False
    
    def show_already_running_message(self):
        """显示程序已在运行的提示信息"""
        try:
            # 创建一个临时的根窗口用于显示消息
            temp_root = tk.Tk()
            temp_root.withdraw()  # 隐藏主窗口
            
            # 设置图标（如果存在）
            icon_path = Path(__file__).parent.parent / "1.ico"
            if icon_path.exists():
                try:
                    temp_root.iconbitmap(str(icon_path))
                except:
                    pass
            
            # 显示消息框
            messagebox.showinfo(
                "程序已在运行",
                "Outlook邮件定时发送工具已在运行中！\n\n"
                "程序将尝试将现有窗口置于前台。\n"
                "如果看不到窗口，请检查任务栏。",
                parent=temp_root
            )
            
            temp_root.destroy()
            
        except Exception as e:
            logger.error(f"显示提示消息失败: {e}")
            # 如果GUI失败，使用控制台输出
            print("程序已在运行中！正在尝试将现有窗口置于前台...")
    
    def cleanup(self):
        """清理锁文件"""
        try:
            if self.lock_file_path and os.path.exists(self.lock_file_path):
                os.remove(self.lock_file_path)
                logger.info("清理锁文件完成")
        except Exception as e:
            logger.error(f"清理锁文件失败: {e}")
    
    def handle_existing_instance(self):
        """处理已存在实例的情况"""
        logger.info("检测到程序已在运行，正在处理...")
        
        # 显示提示信息
        self.show_already_running_message()
        
        # 尝试将现有实例置于前台
        success = self.bring_existing_to_front()
        
        if not success:
            logger.warning("无法将现有实例置于前台，可能窗口已关闭")
        
        # 退出当前实例
        logger.info("退出当前实例")
        sys.exit(0)


# 全局单实例管理器
single_instance_manager = SingleInstanceManager()
