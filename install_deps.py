#!/usr/bin/env python3
"""
依赖安装脚本
解决编码问题，确保依赖正确安装
"""

import subprocess
import sys

def install_package(package):
    """安装单个包"""
    print(f"正在安装: {package}")
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', package
        ], check=True, capture_output=True, text=True, encoding='utf-8')
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False

def main():
    """主函数"""
    print("Outlook邮件定时发送工具 - 依赖安装脚本")
    print("=" * 50)
    
    # 需要安装的包列表
    packages = [
        'pywin32>=305',
        'psutil>=5.9.0',
        'pystray>=0.19.4',
        'Pillow>=9.0.0',
        'pyinstaller>=5.0.0'
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package in packages:
        if install_package(package):
            success_count += 1
        print()  # 空行分隔
    
    print("=" * 50)
    print(f"安装完成: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("✅ 所有依赖安装成功！")
        return 0
    else:
        print("⚠️ 部分依赖安装失败，但可以继续尝试打包")
        return 1

if __name__ == "__main__":
    exit_code = main()
    input("按回车键继续...")
    sys.exit(exit_code)
